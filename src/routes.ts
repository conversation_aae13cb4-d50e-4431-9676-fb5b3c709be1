import { renderNotFound } from '@ice/stark-app';
import BasicLayout from '@/layouts/BasicLayout';

import Home from '@/pages/Home';
import ItemPoolManage from '@/pages/ItemPoolManage';
import ProductListManage from '@/pages/ProductListManage';
import EnterpriseManage from './pages/EnterpriseManage';
import ProjectManage from './pages/ProjectManage';
import MerchantListManage from './pages/MerchantListManage';
import ServicerListManage from './pages/ServicerListManage';
import CorporateBillingManage from './pages/CorporateBillingManage';
import PlatformBillingDetailsManage from './pages/PlatformBillingDetailsManage';
import OrderList from './pages/OrderList';
import OrderDetail from './pages/OrderDetail';
import MerchantQualificationManage from './pages/MerchantQualificationManage';

// 工作台开发指南：https://yuque.antfin-inc.com/xiaoer-docs/dev-guide/pro-code
// 注：路由配置需要和工作台录入的地址保持一致
// 工作台产研平台地址：https://xiaoer-dev.alibaba-inc.com/bzb/console/biz-console/req/list
// 工作台的路由采用受限制度，即该代码下的路由配置本地可运行，不代表工作台可访问，所以需要首先在工作台注册(注册走变更单进行注册，请查看文档)后才能被访问，否则无法访问

const routerConfig = [
  {
    path: '/biz-purchase',
    component: BasicLayout,
    children: [
      {
        path: '/',
        exact: true,
        component: Home,
      },
      {
        path: '/item-pool-manage',
        component: ItemPoolManage,
      },
      {
        path: '/product-list-manage',
        component: ProductListManage,
      },

      {
        path: '/enterprise-manage',
        component: EnterpriseManage,
      },
      {
        path: '/project-manage',
        component: ProjectManage,
      },
      {
        path: '/merchant-list-manage',
        component: MerchantListManage,
      },
      {
        path: '/servicer-list-manage',
        component: ServicerListManage,
      },
      {
        path: '/corporate-billing-manage',
        component: CorporateBillingManage,
      },
      {
        path: '/platform-billing-details-manage',
        component: PlatformBillingDetailsManage,
      },
      {
        path: '/order-list',
        component: OrderList,
      },
      {
        path: '/order-detail',
        component: OrderDetail,
      },
      {
        path: '/merchant_qualification_review',
        component: MerchantQualificationManage,
      },
      {
        path: '*',
        component: () => {
          return renderNotFound();
        },
      },
    ],
  },
];

export default routerConfig;
