.merchantManage {
  min-height: 100vh;
  background-color: #f0f4f7;

  .cardMerchantList {
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .cardTitleContainer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px;
      background-color: #fff;
    }

    .cardTitle {
      color: #111;
      font-size: 18px;
      font-weight: 500;
      line-height: 26px;
    }
    
    .listContainer {
      margin-top: 10px;
    }
  }
  
  .myPagination {
    display: flex;
    justify-content: flex-end;
    margin: 16px 0;
    .totalText {
      margin-left: 16px;
    }
    .nextPaginationPages {
      margin-left: auto;
    }
  }
}

.formItemGroup {
  margin-bottom: 24px;
  padding: 24px;
  background: #fff;
  border-radius: 4px;

  .groupTitle {
    color: #111;
    font-size: 18px;
    font-weight: 500;
    line-height: 26px;
    margin-bottom: 24px;
  }

  .gridContainer {
    padding: 10px;
  }
}

.formSubmitButtonContainer {
  position: absolute;
  bottom: 0;
  width: 100%;
  padding: 10px 16px;
  text-align: center;
  left: 0;
  background: #fff;
  border-top: 1px solid #e8e8e8;
  border-radius: 0 0 4px 4px;
}

.merchantForm {
  :global {
    .next-form-item {
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      
      .next-form-item-label {
        flex: 0 0 auto;
        width: 120px;
        margin-right: 16px;
      }
      
      .next-form-item-control {
        flex: 1 1 auto;
        
        .next-select,
        .next-date-picker2,
        .next-input,
        .next-number-picker {
          width: 100%;
          
          input {
            width: 100%;
          }
        }
      }
    }
  }
}

.inputWidth {
  width: 500px !important;
}
