import React, { useState, useEffect } from 'react';
import { Message } from '@alifd/next';
// @ts-ignore
import MerchantDrawer from '@/components/MerchantListComp/MerchantDrawer';
import MerchantTable from '@/components/MerchantListComp/MerchantTable';
import MerchantPagination from '@/components/MerchantListComp/MerchantPagination';
import { queryMerchantList, deleteMerchant, invalidMerchant } from '@/services';
import { IQueryMerchantListParams, MerchantItem } from '@/types';
import styles from './index.module.scss';

const MerchantList = () => {
    // 商家ID
  const roleId = new URLSearchParams(location.search)?.get('roleId');
  const [visible, setVisible] = useState(false);
  const [currentMerchant, setCurrentMerchant] = useState<any>(null);
  const [mode, setMode] = useState<'edit' | 'view' | 'add'>();
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<MerchantItem[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  const handleClose = () => {
    setVisible(false);
    setCurrentMerchant(null);
    setMode(undefined);
  };

  const fetchMerchantList = async (params?: IQueryMerchantListParams) => {
    try {
      setLoading(true);
      const { current, pageSize } = pagination;
      const response = await queryMerchantList({
        currentPage: current,
        pageSize: pageSize,
        ...params,
      });
      setDataSource(response.data?.dataList || []);
      setPagination({
        ...pagination,
        total: response.data?.totalCount || 0,
      });
    } catch (error) {
      console.error('获取商家列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMerchantList();
  }, [pagination.current, pagination.pageSize]);

  useEffect(() => {
    if (roleId) {
      setCurrentMerchant({ roleId });
      setMode('view');
      setVisible(true);
    }
  }, [roleId]);

  const refresh = () => {
    fetchMerchantList();
  };

  const handleDelete = async (record: MerchantItem) => {
    try {
      const response = await deleteMerchant({roleId: record.roleId, editVersion: record.editVersion});
      if (response.success) {
        Message.success('删除商家成功');
        fetchMerchantList();
      } else {
        Message.error(response.message || '删除商家失败');
      }
    } catch (error) {
      console.error('删除商家失败:', error);
      Message.error('删除商家失败，请稍后重试');
    }
  };

  const handleInvalid = async (record: MerchantItem) => {
    try {
      const response = await invalidMerchant({ roleId: record.roleId, editVersion: record.editVersion });
      if (response.success) {
        Message.success('商家已设为失效状态');
        setTimeout(() => {
          fetchMerchantList();
        }, 1000);
      } else {
        Message.error(response.message || '设置商家失效状态失败');
      }
    } catch (error) {
      console.error('失效商家失败:', error);
      Message.error('设置商家失效状态失败，请稍后重试');
    }
  };

  return (
    <div className={styles.merchantManage}>
      <MerchantTable
        loading={loading}
        dataSource={dataSource}
        onEdit={(record) => {
          setCurrentMerchant(record);
          setMode('edit');
          setVisible(true);
        }}
        onView={(record) => {
          setCurrentMerchant(record);
          setMode('view');
          setVisible(true);
        }}
        onAdd={() => {
          setMode('add');
          setVisible(true);
        }}
        onDelete={handleDelete}
        onInvalid={handleInvalid}
      />
      <MerchantPagination
        pagination={pagination}
        onChange={(current) => setPagination({ ...pagination, current })}
        onPageSizeChange={(pageSize) => setPagination({ ...pagination, pageSize, current: 1 })}
      />
      <MerchantDrawer 
        mode={mode} 
        data={currentMerchant} 
        visible={visible} 
        onClose={handleClose} 
        refresh={refresh}
      />
    </div>
  );
};

export default MerchantList;
