.projectManage {
  min-height: 100vh;
  background-color: #f0f4f7;

  .cardEnterpriseList {
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .cardTitleContainer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px;
      background-color: #fff;
    }

    .cardTitle {
      color: #111;
      font-size: 18px;
      font-weight: 500;
      line-height: 26px;
    }
    
    .listContainer {
      margin-top: 10px;
    }
  }
  
  .myPagination {
    display: flex;
    justify-content: flex-end;
    margin: 16px 0;
    .totalText {
      margin-left: 16px;
    }
    .nextPaginationPages {
      margin-left: auto;
    }
  }
}
