import React, { useState, useEffect } from 'react';
import { Message, Dialog } from '@alifd/next';
import { useLocation } from 'react-router-dom';
import ProjectManageDrawer from '@/components/ProjectComp/ProjectManageDrawer';
import { queryProjectList, deleteProject, invalidProject, queryProjectItemsPool, addProjectItemsPool } from '@/services';
import { queryProjectListResult } from '@/types';
import ProjectTable from '@/components/ProjectComp/ProjectTable';
import ProjectPagination from '@/components/ProjectComp/ProjectPagination';

import { isTrue } from '@/pages/Utils';

import styles from './index.module.scss';


const ProjectManage = () => {
  const location = useLocation();
  const enterpriseId = new URLSearchParams(location.search)?.get('enterpriseId');
  const type = new URLSearchParams(location.search)?.get('type');
  const projectId = new URLSearchParams(location.search)?.get('projectId');
  const [pagination, setPagination] = useState({ current: 1, pageSize: 20, total: 0 });
  const [tableDataSource, setTableDataSource] = useState([]);
  const [loading, setLoading] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [currentProject, setCurrentProject] = useState<any>(null);
  const [drawerMode, setDrawerMode] = useState<'edit' | 'add' | 'view'>();

  const fetchData = async () => {
    setLoading(true);
    try {
      const params: any = {
        currentPage: pagination?.current,
        pageSize: pagination?.pageSize,
      };
      if (enterpriseId) {
        params.enterpriseId = Number(enterpriseId);
      }
      const result: queryProjectListResult = await queryProjectList(params);
      setTableDataSource(result.data?.dataList || []);
      setPagination((prev) => ({ ...prev, total: result.data?.totalCount || 0 }));
    } catch (error) {
      console.error('Failed to fetch project list:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (projectId) {
      setCurrentProject({ projectId });
      setDrawerVisible(true);
      setDrawerMode('view');
    }else if (type === 'add') {
      setDrawerVisible(true);
      setDrawerMode('add');
      setCurrentProject(null);
    }
  }, [projectId, type]);

  useEffect(() => {
      fetchData();
  }, [pagination.current, pagination.pageSize]);

  const handlePaginationChange = (current, pageSize) => {
    setPagination({ ...pagination, current, pageSize });
  };

  const handleDelete = async (record: any) => {
    try {
     const result = await deleteProject({projectId: record?.projectId, editVersion: record?.editVersion});
     if (isTrue(result?.success)) {
      Message.success('项目删除成功');
      fetchData();
     }else {
      Message.error(result?.message || '删除项目失败');
     }
    } catch (error) {
      Message.error('删除项目失败');
      console.error('Failed to delete project:', error);
    }
  };

  const handleInvalid = async (record: any) => {
    try {
     const result = await invalidProject({projectId: record?.projectId, editVersion: record?.editVersion});
     if (isTrue(result?.success)) {
      Message.success('项目已失效');
      setTimeout(() => {
        fetchData();
      }, 1000);
     }else {
      Message.error(result?.message || '项目失效操作失败');
     }
    } catch (error) {
      Message.error('项目失效操作失败');
      console.error('Failed to invalidate project:', error);
    }
  };

  const handleAddProjectItemsPool = async (record: any) => {
    try {
      const result = await addProjectItemsPool({projectId: record?.projectId });
      if (isTrue(result?.success)) {
        Message.success('新增项目商品池成功');
        setTimeout(() => {
          fetchData();
        }, 1000);
        return result;
      } else {
        Message.error(result?.message || '新增项目商品池失败');
      }
    } catch (error) {
      Message.error('新增项目商品池失败');
      console.error('Failed to add project items pool:', error);
    }
  }

  return (
    <div className={styles.projectManage}>
      <ProjectTable 
        dataSource={tableDataSource} 
        loading={loading} 
        handleAddProjectItemsPool={handleAddProjectItemsPool}
        onNewProject={() => {
          setDrawerVisible(true);
          setDrawerMode('add');
          setCurrentProject(null);
        }}
        onEditProject={(record) => { 
          setCurrentProject(record);
          setDrawerVisible(true);
          setDrawerMode('edit');
        }}
        onViewProject={(record) => {
          setCurrentProject(record);
          setDrawerVisible(true);
          setDrawerMode('view');
        }}
        onDeleteProject={(record) => {
          Dialog.confirm({
            title: '确认删除',
            content: '确定要删除该项目吗？此操作不可恢复。',
            onOk: () => handleDelete(record)
          });
        }}
        onInvalidProject={(record) => {
          Dialog.confirm({
            title: '确认失效',
            content: '失效后不可恢复，是否确认操作？',
            onOk: () => handleInvalid(record)
          });
        }}
      />
      <ProjectPagination 
        pagination={pagination} 
        onChange={handlePaginationChange} 
      />
      <ProjectManageDrawer 
        onClose={() => {
          setDrawerVisible(false)
          setDrawerMode(undefined);
          setCurrentProject(null);
        }} 
        visible={drawerVisible}  
        enterpriseId={Number(enterpriseId)}
        projectId={currentProject?.projectId} 
        mode={drawerMode}
        refresh={fetchData}
      />
    </div>
  );
};

export default ProjectManage;
