enum OrderStatus {
  ToBeConfirmed = 0,
  Invisible = 1,
  Confirmed = 2,
  Acquired = 3,
  SettledOrder = 4,
}

export const OrderStatusMap = {
  [OrderStatus.ToBeConfirmed]: '订单可见待确认',
  [OrderStatus.Invisible]: '订单不可见',
  [OrderStatus.Confirmed]: '订单已确认',
  [OrderStatus.Acquired]: '结算已收单',
  [OrderStatus.SettledOrder]: '已结算',
}

enum PayStatus {
  PaymentPending = 1,
  WaitingForDelivery = 2,
  TransactionClosed = 4,
  TransactionSuccess = 6,
  AlipayTransactionNotCreated = 7,
  TransactionClosedByTB = 8,
  PaymentNotAvailable = 9,
}

export const PayStatusMap = {
  [PayStatus.PaymentPending]: '未冻结/未付款→等待买家发起付款',
  [PayStatus.WaitingForDelivery]: '已冻结/已付款/信用额度占用→等待卖家发货',
  [PayStatus.TransactionClosed]: '已退款→交易关闭',
  [PayStatus.TransactionSuccess]: '已转交易/信用额度释放→交易成功',
  [PayStatus.AlipayTransactionNotCreated]: '没有创建外部交易(支付宝交易)',
  [PayStatus.TransactionClosedByTB]: '交易被淘宝关闭',
  [PayStatus.PaymentNotAvailable]: '不可付款/信用额度不足',
}

enum LogisticsStatus {
  WaitingForDelivery = 1,
  WaitingForReceipt = 2,
  Received = 3,
  Returned = 4,
  PartialReceived = 5,
  PartialDelivery = 6,
  NotCreate = 8,
  InDistribution = 9,
}

export const LogisticsStatusMap = {
  [LogisticsStatus.WaitingForDelivery]: '未发货→等待卖家发货',
  [LogisticsStatus.WaitingForReceipt]: '已发货→等待买家确认收货',
  [LogisticsStatus.Received]: '已收货→交易成功',
  [LogisticsStatus.Returned]: '已退货→交易失败',
  [LogisticsStatus.PartialReceived]: '部分收货→交易成功',
  [LogisticsStatus.PartialDelivery]: '部分发货中',
  [LogisticsStatus.NotCreate]: '未创建物流单',
  [LogisticsStatus.InDistribution]: '配货中',
}
