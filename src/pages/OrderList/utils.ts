/**
 * 将状态映射对象转换为 select 可用值
 * @param statusMap
 *   @example { '0': '未开始', '1': '进行中' }
 * @returns 返回标准格式的选项列表数组，每个元素包含：
 *   @property value
 *   @property label
 */
export const formatStatusList = (statusMap) => {
  const list: Array<{ value: number; label: string; }> = []
  Object.keys(statusMap).forEach(item => {
    list.push({
      value: Number(item),
      label: statusMap[item]
    })
  })
  return list;
}

/**
 * 格式化企业购订单数据为 Excel 工作表数据结构
 * @param list 订单数据列表
 * @param buyerInfo 买家信息（企业ID+名称组合字符串），默认为空字符串
 * @returns 二维数组结构的工作表数据，首行为中文表头，后续为数据行
 */

export const formatXlsxData = (list, buyerInfo = '') => {
  // 定义中文表头映射
  const headers = {
    bizOrderId: '企业购订单ID',
    outOrderId: '外部订单ID',
    xxx: '企业ID+名称',
    createTime: '订单创建时间',
    status: '订单状态',
    payStatus: '订单支付状态',
    logisticsStatus: '订单物流状态',
    contractNo: '合同(项目)ID',
    outServiceId: '商机归属服务商ID',
    outServiceNick: '商机归属服务商名称'
  };

  const worksheetData = [
    Object.values(headers), // 表头行
    ...list.map(item => [
      item.bizOrderId.toString(),
      item.outOrderId,
      buyerInfo,
      item.createTime,
      item.status,
      item.payStatus,
      item.logisticsStatus,
      item.contractNo,
      item.outServiceId,
      item.outServiceNick,
    ])
  ];

  return worksheetData;
}

const isEmptyArray = (arr) => {
  return Array.isArray(arr) && arr.length === 0;
}

/**
 * 移除参数的空值
 */
export const removeUndefinedKeys = (obj) => {
  return Object.keys(obj).reduce((acc, key) => {
    if (obj[key] !== undefined && obj[key] !== '' && !isEmptyArray(obj[key])) {
        acc[key] = obj[key];
    }
    return acc;
  }, {});
}
