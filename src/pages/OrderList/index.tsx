import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Box, Table, Pagination, Button, DatePicker, Form, Input, Card, Icon, Message, Field } from '@alifd/next';
import { Select } from '@alifd/next';
import moment from 'moment';
import * as XLSX from 'xlsx';

import { queryEnterpriseSelect, queryEnterpriseOrderList } from '@/services';
import { queryEnterpriseOrderListQuery } from '@/types';

import { LogisticsStatusMap, OrderStatusMap, PayStatusMap } from './constants';
import { formatStatusList, formatXlsxData, removeUndefinedKeys } from './utils';

import styles from './index.module.scss';

moment.locale('zh-cn');

const OrderManagement = () => {
  const [isLoading, setLoading] = useState(false);
  const hasInitRef = useRef(false);
  const [pageInfo, setPageInfo] = useState({ cur: 1, size: 20, total: 0})
  const [searchParams, setSearchParams] = useState<queryEnterpriseOrderListQuery>({});
  const [listData, setListData] = useState<any[]>([]);
  const [enterpriseList, setEnterpriseList] = useState<Array<{label: string, value: number}>>([]);

  const fields = Field.useField();

  const currentEnterpriseIdAndName = useMemo(() => {
    const item = enterpriseList.find(i => i.value === searchParams?.buyerId)
    return item?.label
  }, [searchParams])

  const renderStatusCell = (statusMap, val) => {
    return <>{statusMap[val]}</>
  };

  const columns = [
    { title: '企业购订单ID', dataIndex: 'bizOrderId', width: 180, lock: "left" },
    { title: '外部订单ID', dataIndex: 'outOrderId', width: 120 },
    { title: '企业ID+名称',  width: 150, cell: () => <div>{ currentEnterpriseIdAndName }</div> },
    { title: '订单创建时间', dataIndex: 'createTime', width: 180 },
    {
      title: '订单状态',
      dataIndex: 'status',
      width: 90,
      cell: (val) => renderStatusCell(OrderStatusMap, val),
    },
    {
      title: '订单支付状态',
      dataIndex: 'payStatus',
      width: 110,
      cell: (val) => renderStatusCell(PayStatusMap, val),
    },
    {
      title: '订单物流状态',
      dataIndex: 'logisticsStatus',
      width: 110,
      cell: (val) => renderStatusCell(LogisticsStatusMap, val),
    },
    { title: '合同(项目)ID', dataIndex: 'contractNo', width: 110 },
    { title: '商机归属服务商ID', dataIndex: 'outServiceId', width: 80, cell: (val) => val || '--' },
    { title: '商机归属服务商名称', dataIndex: 'outServiceNick', width: 120, cell: (val) => val || '--'  },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 100,
      cell: (_v, _r, record) => <a href={`/biz-purchase/order-detail?orderId=${record.bizOrderId}&buyerId=${searchParams.buyerId}&buyerName=${currentEnterpriseIdAndName}`} target='_blank'>查看详情</a>,
      lock: "right"
    }
  ];

  // 企业信息获取
  const fetchEnterpriseList = useCallback(async () => {
    try {
      const list = await queryEnterpriseSelect()
      const data = list?.data?.map(item => {
        return {
          label: `【${item?.roleId}】${item?.name}` || '',
          value: item?.roleId || '',
        }
      }) || []
      setLoading(false);
      setEnterpriseList(data)
      hasInitRef.current = true;
      fields.setValue('buyerId', data?.[0]?.value);
      setSearchParams({
        ...searchParams,
        buyerId: data?.[0]?.value
      })
    } catch (err) {
      Message.error('企业信息获取失败，请刷新后重试')
      setLoading(false);
    }
  }, [])

  useEffect(() => {
    setLoading(true);
    fetchEnterpriseList();
  }, [])

  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const data = await queryEnterpriseOrderList({
        contractOrderListReq: {
          ...searchParams,
        },
        pageQueryParam: {
          pageSize: pageInfo.size,
          currentPage: pageInfo.cur,
        }

      });
      setLoading(false);
      if (!data?.success) {
        Message.error(data.errorMsg || '列表拉取失败，请稍后重试');
        setListData(data?.pageData?.data || [])
        setPageInfo(prev => ({
          ...prev,
          cur: 1,
          total: 0,
        }))
        return;
      }
      setListData(data?.pageData?.data || [])
      setPageInfo(prev => ({
        ...prev,
        cur: data?.pageData?.currentPage || 1,
        size: data?.pageData?.pageSize || 20,
        total: data?.pageData?.totalCount || 0,
      }))
    } catch (err) {
      Message.error('列表拉取失败，请稍后重试');
      setLoading(false);
    }
  }, [searchParams, pageInfo])

  const handleSearch = useCallback(async (values, errors) => {
    if (errors) return

    const val: queryEnterpriseOrderListQuery = removeUndefinedKeys({ ...values });

    if (val.createTime) {
      val.createTimeStart = values?.createTime?.[0].format('YYYY-MM-DD HH:mm:ss');
      val.createTimeEnd = values?.createTime?.[1].format('YYYY-MM-DD HH:mm:ss');
      delete val?.createTime;
    }

    if (val.bizOrderIdList) {
      val.bizOrderIdList = (val.bizOrderIdList as string).split(',');
    }
    if (val.outOrderIdList) {
      val.outOrderIdList = (val.outOrderIdList as string).split(',');
    }

    setSearchParams(val);
    setPageInfo(prev => ({ ...prev, cur: 1}))
  }, [])

  useEffect(() => {
    if (!hasInitRef.current) return;
    fetchData()
  }, [pageInfo.cur, pageInfo.size, searchParams])

  const handleExport = useCallback(async () => {
    if (!listData.length) {
      Message.notice('暂无数据，无法导出');
      return
    }

    const formatData = formatXlsxData(listData, currentEnterpriseIdAndName)
    const worksheet = XLSX.utils.aoa_to_sheet(formatData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, '企业购订单');
    XLSX.writeFile(workbook, '企业购订单.xlsx');

  }, [listData, currentEnterpriseIdAndName])

  return (
    <div className={styles.container}>
      <div className={styles.searchArea}>
        <Form field={fields}>
          <Box spacing={20} direction="row" wrap>
            <div className={styles.searchItem}>
              <Form.Item label="企业ID+名称：" name="buyerId" required requiredMessage='请选择企业信息'>
                <Select
                  showSearch
                  dataSource={enterpriseList}
                  style={{ width: 230, marginRight: 8 }}
                />
              </Form.Item>
            </div>
            <div className={styles.searchItem}>
              <Form.Item label="企业购订单ID：" name="bizOrderIdList">
                <Input placeholder="请输入" style={{ width: 150 }} hasClear />
              </Form.Item>
            </div>
            <div className={styles.searchItem}>
              <Form.Item label="外部订单ID：" name="outOrderIdList">
                <Input placeholder="请输入" style={{ width: 150 }} hasClear />
              </Form.Item>
            </div>
            <div className={styles.searchItem}>
              <Form.Item label="订单创建时间：" name="createTime">
                <DatePicker.RangePicker
                  style={{ width: 350 }}
                  placeholder={['请选择时间', '请选择时间']}
                  showTime
                />
              </Form.Item>
            </div>
            <div className={styles.searchItem}>
              <Form.Item label="订单状态：" name="status">
                <Select placeholder="请选择" style={{ width: 150 }} hasClear>
                  {
                    formatStatusList(OrderStatusMap).map((item, idx) => <Select.Option value={item.value} key={idx}>{item.label}</Select.Option>)
                  }
                </Select>
              </Form.Item>
            </div>
            <div className={styles.searchItem}>
              <Form.Item label="物流状态：" name="logisticsStatus">
                <Select placeholder="请选择" style={{ width: 200 }} hasClear>
                  {
                    formatStatusList(LogisticsStatusMap).map((item, idx) => <Select.Option value={item.value} key={idx}>{item.label}</Select.Option>)
                  }
                </Select>
              </Form.Item>
            </div>
            <div className={styles.searchButtons}>
              <Form.Item label=" ">
                <Box spacing={8} direction="row">
                  <Form.Submit
                    type="primary"
                    validate
                    onClick={handleSearch}
                    style={{ marginRight: 8 }}
                    disabled={isLoading}
                  >
                    查询
                  </Form.Submit>
                  <Form.Reset disabled={isLoading}>重置</Form.Reset>
                </Box>
              </Form.Item>
            </div>
          </Box>
        </Form>
      </div>

      <Card title={`企业购订单(${pageInfo.total})`} free className={styles.tableArea} extra={
        <Button onClick={() => { handleExport() }}><Icon type="download"/>导出</Button>
      }>
        <Table
          dataSource={listData}
          columns={columns}
          primaryKey="orderId"
          className={styles.orderTable}
          loading={isLoading}
        />
        <div className={styles.pagination}>
          <Pagination
            total={pageInfo.total}
            pageSize={pageInfo.size}
            current={pageInfo.cur}
            onChange={(page) => {
              window.scrollTo(0, 0)
              setPageInfo({
                ...pageInfo,
                cur: page
              })
            }}
          />
          <Select defaultValue={20} style={{ width: 100 }} onChange={(size: number) => { setPageInfo({ ...pageInfo, size }) }}>
            <Select.Option value={10}>10条/页</Select.Option>
            <Select.Option value={20}>20条/页</Select.Option>
          </Select>
        </div>
      </Card>
    </div>
  );
};

export default OrderManagement;