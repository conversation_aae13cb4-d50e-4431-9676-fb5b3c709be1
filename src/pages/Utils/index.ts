export const applyStatusOptions = [
  { value: 'edit', label: '待编辑', color: 'gray' },
  { value: 'waiting', label: '待报名', color: 'blue' },
  { value: 'processing', label: '报名中', color: 'orange' },
  { value: 'completed', label: '已报名', color: 'green' },
  { value: 'fail', label: '报名失败', color: 'red' },
];

export const auditStatusOptions = [
  { value: 'auditing', label: '待审核', color: 'yellow' },
  { value: 'success', label: '审核通过', color: 'green' },
  { value: 'fail', label: '审核驳回', color: 'red' },
];

export const egoItemStatusOptions = [
  { value: 'online', label: '上架', color: 'green' },
  { value: 'offline', label: '下架', color: 'red' },
  { value: 'online_fail', label: '上架失败', color: 'red' },
];

export const isTrue = (value: string | boolean) => {
  return value === 'true' || value === true;
};

export const security = () => {
  const script = document.createElement('script');
  script.src = '//o.alicdn.com/tbpc/securitySDK/securitySDK.umd.js';
  script.charset = 'UTF-8';
  script.crossOrigin = 'anonymous';
  script.onload = () => {
    try {
      const SecuritySDK = window.securitySDK?.securitySDK;
      if (SecuritySDK) {
        const sdk = new SecuritySDK({
          piskMode: 'ALL',
          bizCode: 'enterprise_purchase',
        });
        sdk.start();
      }
    } catch (e) {}
  };
  document.head.appendChild(script);
};

// 将后台返回的树状数据转换为前端需要的格式
export function transformTreeData(data: any[]) {
  // 清理和重构数据的辅助函数
  function processNode(node: any) {
    const result: any = {
      label: node.categoryName,
      value: node.categoryId?.toString(),
      children: []
    };
    
    // 递归获取所有父节点ID
    let currentParent = node.parent;
    const parentIds: Array<{id: string|number, name: string}> = [];
    while (currentParent) {
      parentIds.unshift({
        id: currentParent.categoryId?.toString(),
        name: currentParent.categoryName
      });
      currentParent = currentParent.parent;
    }
    if (parentIds.length > 0) {
      result.parentIds = parentIds;
    }
    
    return result;
  }

  // 处理所有节点
  const flatNodes = data.map(node => processNode(node));
  const nodesMap = new Map();
  
  // 创建节点映射
  flatNodes.forEach(node => {
    nodesMap.set(node.value, node);
  });
  
  // 构建树结构
  const tree: any[] = [];
  flatNodes.forEach(node => {
    if (node.parentIds) {
      // 遍历父节点ID数组，确保所有层级的节点都被创建
      let currentLevel = tree;
      node.parentIds.forEach(({id, name}) => {
        let parentNode = nodesMap.get(id);
        if (!parentNode) {
          // 如果父节点不存在于原始数据中，创建一个新的父节点
          parentNode = {
            label: name,
            value: id?.toString() ,
            children: []
          };
          nodesMap.set(id, parentNode);
          
          // 将新创建的父节点添加到正确的层级
          let existingNode = currentLevel.find(n => n.value === id);
          if (!existingNode) {
            currentLevel.push(parentNode);
          }
        }
        currentLevel = parentNode.children;
      });
      
      // 将当前节点添加到最后一层父节点的children中
      currentLevel.push(node);
    } else {
      tree.push(node);
    }
    // 清理临时使用的 parentIds
    delete node.parentIds;
  });
  return tree?.[0]?.children;
}

// 格式化时间戳
export const formatTimestamp = (timestamp: number) => {
  if (!timestamp) return '-'; 
  return new Date(timestamp).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
  }).replace(/\//g, '-');
};

export const validatePhoneNumber = (value: string | number, mobileOnly: boolean = false) => {
  if (!value) {
    return Promise.reject('请输入电话号码');
  }
  // 手机号码：1开头的11位数字
  const mobileRegex = /^1[3-9]\d{9}$/;
  // 座机号码：区号3-4位（可选）+ 号码7-8位，可以用-分隔
  const landlineRegex = /^(0\d{2,3}-?)?\d{7,8}$/;
  
  const valueStr = value?.toString();
  if (mobileOnly) {
    if (!mobileRegex.test(valueStr)) {
      return Promise.reject('请输入正确的手机号码');
    }
  } else if (!mobileRegex.test(valueStr) && !landlineRegex.test(valueStr)) {
    return Promise.reject('请输入正确的电话号码（手机号或座机号）');
  }
  return Promise.resolve();
};

export const validateEmail = (value: string | number) => {
  if (!value) {
    return Promise.resolve();
  }
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(value?.toString())) {
    return Promise.reject('请输入正确的邮箱');
  }
  return Promise.resolve();
};

/**
 * Validates a Chinese Unified Social Credit Code
 * @param value The code to validate
 * @returns A Promise that resolves if valid, rejects with error message if invalid
 */
export const validateSocialCreditCode = (value: string | undefined): Promise<void> => {
  if (!value) {
    return Promise.reject('请输入统一社会信用代码');
  }
  const creditCodeRegex = /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/;
  if (!creditCodeRegex.test(value.toString())) {
    return Promise.reject('请输入正确的统一社会信用代码（18位字符）');
  }
  return Promise.resolve();
};

// 企业审核状态
export const EnterpriseAuditStatus = {
  /**
   * 无审核流
   */
  INIT: { code: 0, value: "INIT", desc: "待提交审核" },

  /**
   * 审核中
   */
  AUDITING: { code: 1, value: "AUDITING", desc: "审核中" },

  /**
   * 审核成功
   */
  SUCCESS: { code: 2, value: "SUCCESS", desc: "审核成功" },

  /**
   * 审核失败
   */
  FAIL: { code: 3, value: "FAIL", desc: "审核驳回" }
};

/**
 * 操作项枚举
 */
export enum OperationType {
  /**
   * 保存
   */
  SAVE = 'SAVE',
  /**
   * 提交
   */
  SUBMIT = 'SUBMIT',
  /**
   * 撤回审核
   */
  CANCEL = 'CANCEL',
  /**
   * 审核
   */
  AUDIT = 'AUDIT',
  /**
   * 删除
   */
  DELETE = 'DELETE',
  /**
   * 失效
   */
  INVALIDATE = 'INVALIDATE',
  /**
   * 查看
   */
  VIEW = 'VIEW'
}

/**
 * 操作项列表
 */
export const operationList = [
  OperationType.SAVE,
  OperationType.SUBMIT,
  OperationType.CANCEL,
  OperationType.AUDIT,
  OperationType.DELETE,
  OperationType.INVALIDATE,
  OperationType.VIEW
];
