
import React, { useState, useEffect } from 'react';
import { Button, Select, Input, Form, Field, Grid, Tag, Message, Tab, Icon, Balloon } from '@alifd/next';
import { queryMerchantQualificationList, auditAll } from '@/services';
import { cancelQualification, queryQualificationCount } from '@/services/merchantQualification';
import { formatTimestamp } from '@/pages/Utils';
import QualificationDrawer from '@/components/MerchantQualificationComp/QualificationDrawer';
import AuditDialog from '@/components/MerchantQualificationComp/AuditDialog';
import CancelDialog from '@/components/MerchantQualificationComp/CancelDialog';
import QualificationPagination from '@/components/MerchantQualificationComp/QualificationPagination';
import styles from './index.module.scss';
import OptimizedAdaptiveTable from '@/components/OptimizedAdaptiveTable';

const MerchantQualificationManage = () => {
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [currentApplyId, setCurrentApplyId] = useState('');
  const [auditDialogVisible, setAuditDialogVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState<any>(null);
  const [cancelDialogVisible, setCancelDialogVisible] = useState(false);
  const [countList, setCountList] = useState<Array<{ type: string; count: number }>>([]);
  const [activeTab, setActiveTab] = useState('all');

  const field = Field.useField();

  // 授权状态选项
  const licenseStatusOptions = [
    { value: 'waiting', label: '授权待生效', color: 'orange' },
    { value: 'effective', label: '授权已生效', color: 'green' },
    { value: 'cancel', label: '授权取消', color: 'red' },
    { value: 'invalid', label: '授权失效', color: 'red' }
  ];

  // 审核状态选项
  const auditStatusOptions = [
    { value: 'all', label: '全部', color: 'blue' },
    { value: 'auditing', label: '审核中', color: 'orange' },
    { value: 'success', label: '审核成功', color: 'green' },
    { value: 'fail', label: '审核驳回', color: 'red' },
    { value: 'init', label: '待提交审核', color: 'green' }
  ];

  // 获取资质数量统计
  const fetchQualificationCount = async (values = {}) => {
    try {
      const params = {
        ...values,
      };
      const response = await queryQualificationCount(params);
      if (response.success && response.data && response.data.countList) {
        setCountList(response.data.countList);
      }
    } catch (error) {
      console.error('获取资质数量统计失败:', error);
    }
  };

  const fetchList = async (values = {}) => {
    setLoading(true);
    try {
      const { current, pageSize } = pagination;
      const params: any = {
        currentPage: current,
        pageSize,
        ...values,
      };

      // 如果选择了特定的审核状态，添加到查询参数
      if (activeTab !== 'all') {
        params.auditStatus = activeTab;
      }

      const response = await queryMerchantQualificationList(params);
      setDataSource(response.data?.dataList || []);
      setPagination({
        ...pagination,
        total: response.data?.totalCount || 0,
      });

      // 更新统计数据
      fetchQualificationCount(values);
    } catch (error) {
      console.error('获取商家资质列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchQualificationCount(field.getValues());
  }, []);

  useEffect(() => {
    fetchList(field.getValues());
  }, [pagination.current, pagination.pageSize, activeTab]);

  const handleSearch = () => {
    setPagination({ ...pagination, current: 1 });
    const values = field.getValues();
    fetchList(values);
  };

  const handleReset = () => {
    field.resetToDefault();
    setPagination({ ...pagination, current: 1 });
    fetchList({});
  };

  // 获取特定状态的数量
  const getCountByType = (type: string): number => {
    const item = countList.find(item => item.type === type);
    return item ? item.count : 0;
  };

  const formItemLayout = {
    labelCol: {
      fixedSpan: 8,
    },
    wrapperCol: {
      span: 16,
    },
  };

  const columns = [
    { title: '申请Id', dataIndex: 'id', width: 100, lock: 'left' },
    { title: 'sellerId', dataIndex: 'sellerId', width: 100, lock: 'left' },
    { title: '商家名称', dataIndex: 'sellerName', width: 150 },
    { title: '品牌名称', dataIndex: 'brandName', width: 150 },
    {
      title: '品牌logo',
      dataIndex: 'brandLogoUrl',
      width: 120,
      cell: (value: string) => {
        return value ? (
          <img
            src={value}
            alt="品牌logo"
            style={{ maxWidth: '80px', maxHeight: '40px', objectFit: 'contain' }}
          />
        ) : null;
      }
    },
    { title: '商标注册号', dataIndex: 'registerId', width: 150 },
    {
      title: '授权状态',
      dataIndex: 'licenseStatus',
      width: 150,
      cell: (value: string, _: number, record: any) => {
        const status = licenseStatusOptions.find(item => item.value === value);
        return (
          <div className={styles.statusContainer}>
            {status && <Tag color={status.color} size="small">{status.label}</Tag>}
          </div>
        );
      }
    },
    {
      title: '资质生效时间段',
      dataIndex: 'effectiveTime',
      width: 200,
      cell: (_: any, __: number, record: any) => (
        <span>{formatTimestamp(record.licenseStartTime)} ~ {formatTimestamp(record.licenseEndTime)}</span>
      )
    },
    {
      title: '类目范围',
      dataIndex: 'licenseType',
      width: 150,
      cell: (value: string) => value === 'all' ? '品牌下全部类目' : '指定类目'
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      width: 140,
      cell: (value: string, _: number, record: any) => {
        const status = auditStatusOptions.find(item => item.value === value);
        return (
          <div className={styles.statusCell}>
            <div className={styles.statusContainer}>
              {status && <Tag color={status.color} size="small">{status.label}</Tag>}
              {value === 'fail' && record.rejectReason && (
                <Balloon.Tooltip
                  trigger={<Icon type="help" size="small" style={{ marginLeft: '4px', cursor: 'pointer' }} />}
                  align="t"
                >
                  {record.rejectReason}
                </Balloon.Tooltip>
              )}
            </div>
            <br />
            {record.auditUrl && (
              <a
                href={record.auditUrl}
                target="_blank"
                rel="noopener noreferrer"
                className={styles.approvalLink}
              >
                查看审批流
              </a>
            )}
          </div>
        );
      }
    },
    { title: '最近操作人', dataIndex: 'updateOperator', width: 120 },
    {
      title: '最近操作时间',
      dataIndex: 'gmtModified',
      width: 160,
      cell: (value: number) => formatTimestamp(value)
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 150,
      lock: 'right',
      cell: (_: any, __: number, record: any) => {
        const operationList = record?.operateList || [];
        return (
          <div>
            {operationList.includes('view') && (
              <Button
                text
                type="primary"
                onClick={() => handleView(record)}
                style={{ marginRight: '10px' }}
              >
                查看
              </Button>
            )}
            {operationList.includes('audit') && (
              <Button
                text
                type="primary"
                onClick={() => handleAudit(record)}
                style={{ marginRight: '10px' }}
              >
                审核
              </Button>
            )}
            {operationList.includes('reject') && (
              <Button
                text
                type="primary"
                onClick={() => handleCancel(record)}
              >
                取消授权
              </Button>
            )}
          </div>
        );
      },
    },
  ];

  const handleView = (record: any) => {
    setCurrentApplyId(record.id);
    setDrawerVisible(true);
  };

  const handleCloseDrawer = () => {
    setDrawerVisible(false);
    setCurrentApplyId('');
  };

  const handleAudit = (record: any) => {
    setCurrentRecord(record);
    setAuditDialogVisible(true);
  };

  const handleAuditSubmit = async (values: any) => {
    try {
      if (!currentRecord) {
        Message.error('当前记录不存在');
        return;
      }

      const result = await auditAll({
        instanceType: 'bpms',
        targetType: 'brand_licence',
        auditType: values.auditStatus === 'APPROVED' ? 'approve' : 'reject',
        targetIdList: [String(currentRecord.id)],
        mark: values.auditStatus === 'REJECTED' ? values.rejectReason : ''
      });

      if (result.success) {
        Message.success('审核操作成功');
        setAuditDialogVisible(false);
        // 延迟刷新列表和统计数据
        setTimeout(() => {
          fetchList(field.getValues());
        }, 1000);
      } else {
        Message.error(result?.message || '审核操作失败');
      }
    } catch (error) {
      console.error('审核操作失败:', error);
      Message.error('审核操作失败');
    }
  };

  const handleCancel = (record: any) => {
    setCurrentRecord(record);
    setCancelDialogVisible(true);
  };

  const handleCancelSubmit = async (reason: string) => {
    try {
      if (!currentRecord) {
        Message.error('当前记录不存在');
        return;
      }

      const result = await cancelQualification({
        id: currentRecord.id,
        reason
      });

      if (result.success) {
        Message.success('取消授权成功');
        setCancelDialogVisible(false);
        // 延迟刷新列表和统计数据
        setTimeout(() => {
          fetchList(field.getValues());
        }, 1000);
      } else {
        Message.error(result?.message || '取消授权失败');
      }
    } catch (error) {
      console.error('取消授权失败:', error);
      Message.error('取消授权失败');
    }
  };

  return (
    <div className={styles.qualificationManage}>
      <Form field={field} labelAlign="top" {...formItemLayout} className={styles.searchSection}>
        <Grid.Row wrap gutter="10">
          <Grid.Col span={5}>
            <Form.Item label="商家名称" name="sellerName">
              <Input
                placeholder="请输入"
                style={{ width: '100%' }}
                hasClear
              />
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={5}>
            <Form.Item label="品牌：" name="brandName">
              <Input
                placeholder="请输入"
                style={{ width: '100%' }}
                hasClear
              />
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={5}>
            <Form.Item label="授权状态：" name="licenseStatus">
              <Select
                placeholder="全部"
                style={{ width: '100%' }}
                hasClear
                dataSource={licenseStatusOptions}
              />
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={5}>
            <Form.Item label="审核状态" name="auditStatus">
              <Select
                placeholder="全部"
                style={{ width: '100%' }}
                hasClear
                dataSource={auditStatusOptions}
              />
            </Form.Item>
          </Grid.Col>
          <Grid.Col span={4}>
            <Form.Item label=" ">
              <div className={styles.rcodeSearchBtns}>
                <Button
                  type="primary"
                  onClick={handleSearch}
                  style={{ marginRight: '8px' }}
                >
                  查询
                </Button>
                <Button
                  type="normal"
                  onClick={handleReset}
                >
                  重置
                </Button>
              </div>
            </Form.Item>
          </Grid.Col>
        </Grid.Row>
      </Form>

      <div className={styles.cardQualificationList}>
        {/* 添加Tab切换 */}
        <Tab
          shape="text"
          activeKey={activeTab}
          onChange={(key) => {
            setActiveTab(key);
            setPagination({ ...pagination, current: 1 });
          }}
          className={styles.statusTabs}
        >
          {auditStatusOptions.filter(option => option.value !== 'init').map((option) => (
            <Tab.Item
              key={option.value}
              title={
                <span>
                  {option.label}
                  <span style={{ marginLeft: '4px' }}>({getCountByType(option.value)})</span>
                </span>
              }
            />
          ))}
        </Tab>

        <div className={styles.listContainer}>
          <OptimizedAdaptiveTable
            loading={loading}
            dataSource={dataSource}
            columns={columns}
            emptyContent={<div>暂无数据</div>}
          />
          <QualificationPagination
            pagination={pagination}
            onChange={(current) => setPagination({ ...pagination, current })}
            onPageSizeChange={(pageSize) => setPagination({ ...pagination, pageSize, current: 1 })}
          />
        </div>
      </div>

      {/* 添加审核对话框 */}
      <AuditDialog
        visible={auditDialogVisible}
        onClose={() => setAuditDialogVisible(false)}
        onOk={handleAuditSubmit}
      />

      {/* 添加取消授权对话框 */}
      <CancelDialog
        visible={cancelDialogVisible}
        onClose={() => setCancelDialogVisible(false)}
        onOk={handleCancelSubmit}
      />

      {/* 查看抽屉组件 */}
      <QualificationDrawer
        visible={drawerVisible}
        applyId={currentApplyId}
        onClose={handleCloseDrawer}
        onRefresh={() => {
          setTimeout(() => {
            fetchList(field.getValues());
          }, 1000);
        }}
      />
    </div>
  );
};

export default MerchantQualificationManage;

