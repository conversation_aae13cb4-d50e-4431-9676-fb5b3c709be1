
.qualificationManage {
  min-height: 100vh;
  background-color: #f0f4f7;
  padding: 16px;

  :global {
    .next-form {
      background: #fff;
      padding: 16px;
      margin-bottom: 16px;

      .next-form-item {
        margin-right: 16px;
        margin-bottom: 0;
      }

      .next-form-item-label {
        color: #333;
      }

      .next-btn {
        margin-left: 8px;
      }
    }
  }

  .cardQualificationList {
    background: #fff;

    .cardTitleContainer {
      margin-bottom: 16px;

      .cardTitle {
        font-size: 16px;
        font-weight: 500;
        color: #333;
      }
    }

    .listContainer {
      width: 100%;
      position: relative;

      :global {
        .next-table {
          .next-table-header {
            background: #f5f5f5;
          }
        }
      }
    }
  }

  .myPagination {
    margin: 16px 0;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 0 16px 16px;

    :global {
      .next-pagination-display {
        margin-right: 16px;
      }
    }
  }

  .totalText {
    color: #666;
    font-size: 12px;
  }

  .statusContainer {
    display: flex;
    align-items: center;
  }
}

