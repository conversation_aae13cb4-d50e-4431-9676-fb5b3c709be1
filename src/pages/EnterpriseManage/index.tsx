import React, { useState, useEffect } from "react";
import EnterpriseManageDrawer from '@/components/EnterpriseManageDrawer';
import EnterpriseTable from '@/components/EnterpriseManageComp/EnterpriseTable';
import EnterprisePagination from '@/components/EnterpriseManageComp/EnterprisePagination';

import { queryEnterpriseList } from "@/services";

import styles from  "./index.module.scss";


const EnterpriseManage = () => {
  // 企业ID
  const roleId = new URLSearchParams(location.search)?.get('roleId');
  const [pagination, setPagination] = useState({ current: 1, pageSize: 20, total: 0 });
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<any>(null);
  const [drawerMode, setDrawerMode] = useState<'edit' | 'view' | 'add'>();
  const [loading, setLoading] = useState(false);

  const refreshList = async () => {
    setLoading(true);
    try {
      const result = await queryEnterpriseList({
        currentPage: pagination.current,
        pageSize: pagination.pageSize,
      });
      setDataSource(result.data?.dataList || []);
      setPagination((prev) => ({ ...prev, total: result.data?.totalCount || 0}));
    } catch (error) {
      console.error("Failed to fetch enterprise list:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    refreshList();
  }, [pagination.current, pagination.pageSize]);

  useEffect(() => {
    if (roleId) {
      setSelectedRecord({ roleId });
      setDrawerMode('view');
      setDrawerVisible(true);
    }
  }, [roleId]); 

  const handlePaginationChange = (current, pageSize) => {
    setPagination({ ...pagination, current, pageSize });
  };

  const handleAddEnterprise = () => {
    setDrawerMode('add');
    setSelectedRecord(null);
    setDrawerVisible(true);
  };

  const handleEdit = (record) => {
    setSelectedRecord(record);
    setDrawerMode('edit');
    setDrawerVisible(true);
  };

  const handleView = (record) => {
    setSelectedRecord(record);
    setDrawerMode('view');
    setDrawerVisible(true);
  };

  const handleDrawerClose = () => {
    setDrawerVisible(false);
    setSelectedRecord(null);
    setDrawerMode(undefined);
  };

  return <div className={styles.enterpriseManage}>
    <EnterpriseTable
      loading={loading}
      dataSource={dataSource}
      onAddEnterprise={handleAddEnterprise}
      onEdit={handleEdit}
      onView={handleView}
      onRefresh={refreshList}
    />
    <EnterprisePagination
      pagination={pagination}
      onChange={handlePaginationChange}
    />
    <EnterpriseManageDrawer 
      visible={drawerVisible} 
      onClose={handleDrawerClose}
      roleId={selectedRecord?.roleId}
      onRefresh={refreshList}
      mode={drawerMode}
    />
  </div>;
};

export default EnterpriseManage;
