import React, { useState, useEffect } from 'react';
import { Select, Tab } from '@alifd/next';
import PendingPoolList from '@/components/ProductListComp/PendingPoolProductList';
import PooledProductList from '@/components/ProductListComp/PooledProductList';

import { queryItemsPoolBaseInfo, queryProjectSelectList } from '@/services';
import { PoolData, ProjectDetail } from '@/types';
import { isTrue } from '../Utils';

import styles from './index.module.scss';


const ProductList = () => {
  const [activeTab, setActiveTab] = useState('add');
  const [poolId, setPoolId] = useState(new URLSearchParams(location.search)?.get('poolId'));
  const [selectedProject, setSelectedProject] = useState<string>();
  const [poolBaseInfo, setPoolBaseInfo] = useState<PoolData & { fetchPoolBaseInfo: () => void, projectName?: string }>();
  const [projectList, setProjectList] = useState<ProjectDetail[]>([]);

  const fetchData = async () => {
    try {
      const data = await queryItemsPoolBaseInfo({
        poolId: Number(poolId),
      });
      if(isTrue(data?.success)){
        const currentProject = projectList?.find(item => item?.poolId === Number(poolId));
        setPoolBaseInfo({
          ...data?.data,
          poolId: Number(poolId),
          fetchPoolBaseInfo: fetchData,
          projectName: currentProject?.projectName,
        });
      }else{
        setPoolBaseInfo({
          poolId: Number(poolId),
          poolRule: {},
          countList: [],
          project: {},
          fetchPoolBaseInfo: fetchData,
          projectName: undefined,
        })
      }
    } catch (error) {
      setPoolBaseInfo({
        poolId: Number(poolId),
        poolRule: {},
        countList: [],
        project: {},
        fetchPoolBaseInfo: fetchData,
        projectName: undefined,
      })
      console.error('Error fetching pool base info:', error);
    }
  };

  useEffect(() => {
    poolId && fetchData();
    if(poolId){
      const projectId = projectList?.find(item => item?.poolId === Number(poolId))?.projectId;
      projectId && setSelectedProject(String(projectId));
    }
  }, [poolId, projectList]);

  useEffect(() => {
    const fetchProjectList = async () => {
      const data = await queryProjectSelectList({});
      setProjectList(data?.data || []);
      !poolId && setPoolId(data?.data?.[0]?.poolId);
    };

    fetchProjectList();
  }, []);

  return (
    <div className={styles.productListContainer}>
      <div className={styles.projectInfoContainer}>
        <div className={styles.projectInfoItem}>
          <span className={styles.label}>当前项目:</span>
          <Select
            followTrigger
            style={{ width: 200 }}
            value={selectedProject}
            onChange={(val) => {
              setSelectedProject(val as string)
              const newPoolId = projectList?.find(item => item?.projectId === val)?.poolId;
              setPoolId(newPoolId ? String(newPoolId) : null);
              // Update URL parameter
              const searchParams = new URLSearchParams(window.location.search);
              searchParams.set('poolId', newPoolId ? String(newPoolId) : '');
              window.history.replaceState(null, '', `/biz-purchase/product-list-manage?${searchParams.toString()}`);
            }}
            dataSource={projectList?.map(item => ({
              ...item,
              label: item.projectName,
              value: item.projectId,
            }))}
          >
          </Select>
        </div>
        <div className={styles.projectInfoItem}>
          <span className={styles.label}>项目id:</span>
          <span>{selectedProject || '--'}</span>
        </div>
        <div className={styles.projectInfoItem}>
          <span className={styles.label}>归属企业:</span>
          {/* @ts-ignore */}
          <span>{projectList?.find(item => item?.projectId == selectedProject )?.enterpriseName || '--'}</span>
        </div>
      </div>

      <Tab activeKey={activeTab} unmountInactiveTabs  onChange={setActiveTab} navStyle={{ backgroundColor: '#fff' }}>
        <Tab.Item title={`已入池商品 (${poolBaseInfo?.countList?.find(item => item.type === 'add')?.count || 0})`} key="add">
          <PooledProductList poolBaseInfo={poolBaseInfo} setActiveTab={setActiveTab} />
        </Tab.Item>
        <Tab.Item title={`待入池商品 (${poolBaseInfo?.countList?.find(item => item.type === 'waiting')?.count || 0})`} key="waiting">
          <PendingPoolList poolBaseInfo={poolBaseInfo} />
        </Tab.Item>
      </Tab>
    </div>
  );
};

export default ProductList;
