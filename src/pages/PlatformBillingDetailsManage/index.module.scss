.container {
  padding: 24px;
  background: #fff;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;

  h2 {
    font-size: 16px;
    font-weight: 600;
    color: #111;
    margin: 0;
  }
}

.filterSection {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 24px;

  .filterActions {
    display: flex;
    gap: 8px;
  }
  .settleStatusInput {
    width: 100px;
  }
}

.operationColumn {
  display: flex;
  flex-direction: column;
  gap: 8px;
}


.mt6 {
  margin-top: 6px;
  span {
    display: inline-block;
    min-width: 58px;
  }
}

.deleteDialog {
  display: flex;
  align-items: baseline;
  padding: 20px;
}