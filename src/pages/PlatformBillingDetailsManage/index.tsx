import React from 'react';
import { Table, Button, Select, DatePicker, Input, Icon, Dialog, Field, Form } from '@alifd/next';
import clsx from 'clsx';
import moment from 'moment';

import BillTableHeader from './components/BillTableHeader';
import DetailDrawer from './components/DetailDrawer';

import styles from './index.module.scss';
import s from './styles/common.module.scss';

moment.locale('zh-cn');

import { mockData } from './data';
const CorporateBillingManage: React.FC = () => {
  const [confirmModalVisible, setConfirmModalVisible] = React.useState(false);
  const [detailDrawerVisible, setDetailDrawerVisible] = React.useState(false);
  const [currentDetailBill, setCurrentDetailBill] = React.useState<any>(null);
  const [createBillDrawerVisible, setCreateBillDrawerVisible] = React.useState(false);
  const [deleteDialogVisible, setDeleteDialogVisible] = React.useState(false);
  const [overdueOpModalVisible, setOverdueOpModalVisible] = React.useState(false);

  const searchFields = Field.useField();

  const columns = [
    {
      title: '账单明细号',
      dataIndex: 'billId',
      lock: 'left',
      width: 140,
    },
    {
      title: '业务类型',
      dataIndex: 'billName',
      width: 160,
    },
    {
      title: '结算单号',
      dataIndex: 'company',
      width: 200,
    },
    {
      title: '结算模式',
      dataIndex: 'amount',
      width: 120,
      align: 'right',
    },
    {
      title: '结算批次账单号',
      dataIndex: 'billTime',
      width: 120,
      align: 'center',
    },
    {
      title: '所属汇总账单号',
      dataIndex: 'expectedPayTime',
      width: 120,
      align: 'center',
    },
    {
      title: '合同',
      dataIndex: 'expectedPayTime',
      width: 180,

    },
    {
      title: '项目ID',
      dataIndex: 'expectedPayTime',
      width: 120,
      align: 'center',
    },
    {
      title: '结算方式',
      dataIndex: 'expectedPayTime',
      width: 180,

    },
    {
      title: '税票',
      dataIndex: 'expectedPayTime',
      width: 120,
      align: 'center',
    },
    {
      title: '税票号',
      dataIndex: 'expectedPayTime',
      width: 180,

    },
    {
      title: '开票方信息',
      dataIndex: 'expectedPayTime',
      width: 120,
      align: 'center',
    },
    {
      title: '受票方信息',
      width: 180,

    },
    {
      title: '收款方',
      dataIndex: 'expectedPayTime',
      width: 120,
      align: 'center',
    },
    {
      title: '付款方',
      dataIndex: 'expectedPayTime',
      width: 180,

    },
    {
      title: '账单类型',
      dataIndex: 'platformInvoice',
      lock: 'right',
      width: 180,
      cell: (value: any) => (
        <div>
          <div
            className={clsx({
              [s.tag]: true,
              [s.tagOrange]: value.status === '待开票',
              [s.tagGray]: value.status !== '待开票',
            })}
          >
            {value.status}
          </div>
          {value.invoiceNo && (
            <>
              <div className={styles.mt6}>
                <span>发票号</span><span>{value.invoiceNo}</span>
              </div>
              <div className={styles.mt6}>
                <span>开票时间</span><span>{value.invoiceTime}</span>
              </div>
            </>
          )}
        </div>
      )
    },
    {
      title: '票据状态',
      dataIndex: 'customerPayment',
      lock: 'right',
      width: 180,
      cell: (value: any) => (
        <div>
          <div
            className={clsx({
              [s.tag]: true,
              [s.tagGreen]: value.status === '已打款',
              [s.tagGray]: value.status !== '已打款',
            })}
          >
            {value.status}
          </div>
          {value.paymentNo && (
            <>
              <div className={styles.mt6}>
                <span>资金单号</span><span>{value.paymentNo}</span>
              </div>
              <div className={styles.mt6}>
                <span>到账时间</span><span>{value.paymentTime}</span>
              </div>
            </>
          )}
        </div>
      )
    },
    {
      title: '收付款状态',
      dataIndex: 'operation',
      lock: 'right',
      width: 80,
      cell: (value: string[], index: number, record: any) => (
        <div className={styles.operationColumn}>
          {record.customerConfirm.status === '待确认' && (
            <Button
              text
              type="primary"
              onClick={() => handleConfirm(record)}
            >
              代客确认
            </Button>
          )}
          <Button
            text
            type="primary"
            onClick={() => handleViewDetail(record)}
          >
            账单明细
          </Button>
          {value.map((op, idx) => (
            <Button text key={idx} type="primary">
              {op}
            </Button>
          ))}
          <Button
            text
            type="primary"
            onClick={() => { setOverdueOpModalVisible(true) }}
          >
            逾期操作
          </Button>
          <Button
            text
            type="primary"
            onClick={() => handleDelete()}
          >
            删除
          </Button>
        </div>
      )
    },
    {
      title: '操作',
      dataIndex: 'customerPayment',
      lock: 'right',
      width: 180,
      cell: (value: any) => (
        <div>
          <div
            className={clsx({
              [s.tag]: true,
              [s.tagGreen]: value.status === '已打款',
              [s.tagGray]: value.status !== '已打款',
            })}
          >
            {value.status}
          </div>
          {value.paymentNo && (
            <>
              <div className={styles.mt6}>
                <span>资金单号</span><span>{value.paymentNo}</span>
              </div>
              <div className={styles.mt6}>
                <span>到账时间</span><span>{value.paymentTime}</span>
              </div>
            </>
          )}
        </div>
      )
    }
  ];

  const handleConfirm = (record: any) => {
    setConfirmModalVisible(true);
  };

  const handleViewDetail = (record: any) => {
    setCurrentDetailBill(record);
    setDetailDrawerVisible(true);
  };

  const handleDetailDrawerClose = () => {
    setDetailDrawerVisible(false);
  };


  const handleSearchData = () => {
    console.log('=======', searchFields.getValues());
  }

  const handleClearData = () => {
    searchFields.reset();
  }

  // 点击 table 某条删除
  const handleDelete = () => {
    setDeleteDialogVisible(true);
  }
  // 删除弹窗确认
  const handleDelDialogConfirm = () => {
    console.log('====handleDelDialogConfirm confirm');
    setDeleteDialogVisible(false);
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h2>平台账单</h2>
      </div>

      <Form field={searchFields} className={styles.filterSection}>
        <Form.Item name="a">
          <Input placeholder="账单明细号" className={s.inputWrapper} />
        </Form.Item>
        <Form.Item name="c">
          <Select placeholder="账单类型" className={s.selectWrapper} />
        </Form.Item>
        <Form.Item name="d">
          <Input placeholder="收款方账号/ID" className={s.inputWrapper} />
        </Form.Item>
        <Form.Item name="d">
          <Input placeholder="付款方账号/ID" className={s.inputWrapper} />
        </Form.Item>
        <Form.Item  name="f">
          <DatePicker placeholder="出账时间" className={s.datePickerWrapper} />
        </Form.Item>
        <div className={styles.filterActions}>
          <Button type="primary" className={s.btnPrimary} onClick={() => { handleSearchData() }}>搜索</Button>
          <Button className={s.btnText} text  onClick={() => { handleClearData() }}><Icon type="ashbin" />清除条件</Button>
        </div>
      </Form>

      <BillTableHeader cols={columns.map(item => { return { value:  item.dataIndex, label: item.title } })}/>

      <Table
        dataSource={mockData}
        columns={columns}
        className={s.table}
      />


      <Dialog
        visible={deleteDialogVisible}
        onClose={() => { setDeleteDialogVisible(false) }}
        footer={[
          <Button className={clsx(s.btnPrimary, s.btnActive)} onClick={() => { handleDelDialogConfirm() }}>确认</Button>,
          <Button className={clsx(s.btnPrimary, s.btnHighlight, s.ml8)} onClick={() => { setDeleteDialogVisible(false) }}>取消</Button>
        ]}
      >
        <div className={styles.deleteDialog}>
          <div><Icon type="warning"  style={{ color: "#FFA003", marginRight: "10px" }} /></div>
          <div>
            <div className={s.dialogTitle}>是否确认删除</div>
            <div className={s.dialogContent}>一经删除不可修改！请谨慎操作</div>
          </div>
        </div>
      </Dialog>

      <DetailDrawer visible={detailDrawerVisible} onClose={handleDetailDrawerClose} />

    </div>
  );
};

export default CorporateBillingManage;
