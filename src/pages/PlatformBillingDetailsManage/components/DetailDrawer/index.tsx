import React, { useState } from 'react';
import { Drawer, Step, Button, Form, Input, Timeline, Icon, Message } from '@alifd/next';
import clsx from 'clsx';

import styles from './index.module.scss';
import s from '../../styles/common.module.scss';

interface DetailDrawerProps {
  visible: boolean;
  onClose: () => void;
}

const DetailDrawer: React.FC<DetailDrawerProps> = ({
  visible,
  onClose,
}) => {


  return (
    <Drawer
      visible={visible}
      onClose={onClose}
      width={800}
      title="账单明细"
      className={styles.detailDrawer}
    >
      <div className={styles.basicInfo}>
        <h3>基本信息</h3>
        <div className={styles.infoGrid}>
          <div className={styles.infoItem}>
            <span className={styles.label}>账单ID</span>
            <span className={styles.text}>12312312</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>合同号</span>
            <span className={styles.text}>123123123</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>出账日</span>
            <span className={styles.text}>2025-11-12</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>账单开始时间</span>
            <span className={styles.text}>123123</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>账单截止时间</span>
            <span className={styles.text}>123123</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>预计完款日</span>
            <span className={styles.text}>xxxx</span>
          </div>
        </div>
      </div>
      <div className={styles.basicInfo}>
        <h3>税票信息</h3>
        <div className={styles.infoGrid}>
          <div className={styles.infoItem}>
            <span className={styles.label}>账单ID</span>
            <span className={styles.text}>12312312</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>合同号</span>
            <span className={styles.text}>123123123</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>出账日</span>
            <span className={styles.text}>2025-11-12</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>账单开始时间</span>
            <span className={styles.text}>123123</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>账单截止时间</span>
            <span className={styles.text}>123123</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>预计完款日</span>
            <span className={styles.text}>xxxx</span>
          </div>
        </div>
      </div>
      <div className={styles.basicInfo}>
        <h3>资金信息</h3>
        <div className={styles.infoGrid}>
          <div className={styles.infoItem}>
            <span className={styles.label}>账单ID</span>
            <span className={styles.text}>12312312</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>合同号</span>
            <span className={styles.text}>123123123</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>出账日</span>
            <span className={styles.text}>2025-11-12</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>账单开始时间</span>
            <span className={styles.text}>123123</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>账单截止时间</span>
            <span className={styles.text}>123123,123123123123,123123,123123,123123,123123,123123,123123,123123,123123,123123,123123</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>预计完款日</span>
            <span className={styles.text}>xxxx</span>
          </div>
        </div>
      </div>
      <div className={styles.footer}>
        <button className={clsx(s.btnPrimary, s.btnActive)} onClick={() => {}}>确认</button>
        <button className={clsx(s.btnPrimary, s.btnHighlight, s.ml8)} onClick={onClose}>取消</button>
      </div>
    </Drawer>
  );
};

export default DetailDrawer;