.detailDrawer {
  position: relative;
  :global {
    .next-drawer-body {
      padding: 24px;
      height: 100%;
    }
  }

  :global(.next-pagination-display) {
    min-width: 25px;
    margin: 0;
  }
}



.basicInfo {
  margin-bottom: 24px;

  h3 {
    color: #111;
    font-weight: 600;
    font-size: 14px;
    margin: 0 0 16px 0;
  }
}

.infoGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  border-radius: 12px;
}

.infoItem {
  display: flex;
  gap: 8px;
  font-size: 12px;
  color: #111;
  .label {
    color: #999;
    min-width: 100px;
  }
  .text {
    display: block;
    max-width: 150px;
    word-wrap: break-word;
  }
}

.footer {
  position: absolute;
  bottom: 10px;
  right: 10px;
  display: flex;
  justify-content: center;

}