import React, { useEffect, useState } from 'react';
import { Balloon, Button, Checkbox, Icon, Pagination } from '@alifd/next';
import clsx from 'clsx'

import styles from './index.module.scss';
import s from '../../styles/common.module.scss';

interface BillTableHeaderProps {
  cols: Array<{label: string, value: string}>
}

const BillTableHeader: React.FC<BillTableHeaderProps> = ({ cols }) => {
  const [pageInfo, setPageInfo] = useState({
    current: 1,
    total: 100,
    pageSize: 20,
  });
  const [colsSelectVisible, setColsSelectVisible] = useState(false);
  const [colsSelectValue, setColsSelectValue] = useState<string[]>([]);

  const handlePageChange = (cur: number) => {
    setPageInfo({
      ...pageInfo,
      current: cur
    })
    // 发起请求
  }

  useEffect(() => {
    if (!colsSelectVisible) return;
    try {
      const storeCols = JSON.parse(localStorage.getItem('qyg_billing_manage_table_keys') || '[]');
      setColsSelectValue(storeCols)
    } catch (err) {
      console.log('[BillTableHeader]get storage error:', err);
    }
  }, [colsSelectVisible])

  const handleStoreCols = () => {
    try {
      localStorage.setItem('qyg_billing_manage_table_keys', JSON.stringify(colsSelectValue));
      setColsSelectVisible(false);
    } catch (err) {
      console.log('[BillTableHeader]set storage error:', err);
    }
  }

  return <div className={styles.billHeader}>

    <div className={styles.selectRowOpLeft}>
      <div>已选3</div>
      <Button text style={{ color: '#3D5EFF', marginLeft: 8 }}>下载明细</Button>
    </div>
    <div className={styles.selectRowOpRight}>
      <div className={styles.selectRowOpContainer}>
        <Balloon
          className={s.popupContainer}
          visible={colsSelectVisible}
          closable={false}
          onVisibleChange={(val) => { setColsSelectVisible(val) }}
          trigger={<div className={styles.selectRowOp}>选择展示项<Icon type="arrow-down" size="xs" /></div>}
          triggerType="click"
        >
          {
            cols && cols.length && <div className={clsx(styles.selectBox)}>
              <Checkbox.Group
                value={colsSelectValue}
                onChange={(v: string[]) => { setColsSelectValue(v) }}
              >
                {
                  cols.map((item, idx) => <Checkbox className={styles.checkboxItem} value={item.value}> {item.label} </Checkbox>)
                }
              </Checkbox.Group>
              <div className={styles.selectBtnBox}>
                <Button className={clsx(s.btnPrimary, s.btnActive)} onClick={() => { handleStoreCols() }}>确定</Button>
              </div>
            </div>
          }
        </Balloon>
      </div>
      <div className={styles.total}>共 {pageInfo.total} 项数据</div>
      <div className={styles.page}>
        <Pagination
          type="simple"
          shape="arrow-only"
          onChange={(cur) => { handlePageChange(cur) }}
          {...pageInfo}
        />
      </div>
    </div>
  </div>
}

export default BillTableHeader