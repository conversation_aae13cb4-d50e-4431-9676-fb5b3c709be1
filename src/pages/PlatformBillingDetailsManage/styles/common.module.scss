:global(.next-focus) {
  box-shadow: none !important;
}

:global(.next-input-textarea) {
  border: 1px solid #e4e6ed;
  border-radius: 9px;
}

.ml8 {
  margin-left: 8px;
}

.table {
  color: #111;
  border-left: none;
  :global {
    .next-table-cell {
      &.next-table-cell-right {
        text-align: right;
      }

      &.next-table-cell-center {
        text-align: center;
      }
    }
  }
  :global(th) {
    background-color: #f7f8fa;
    border-right: none;
  }
  :global(td) {
    border-right: none;
  }
}


.tag {
  display: inline-flex;
  align-items: center;
  height: 18px;
  padding: 3px 6px;
  border-radius: 6px;
}
.tagGreen {
  color: #31cc31;
  background: rgba(49, 204, 49, 0.06);
}
.tagGray {
  color: #999;
  background-color: #f7f8fa;
}
.tagOrange {
  color: #ff8000;
  background-color: rgba(255, 128, 0, 0.06);
}

.btnPrimary {
  padding: 0 20px !important;
  height: 36px !important;
  border-radius: 100px !important;
  background-color: #f0f2fa !important;
  color: #111 !important;
  font-size: 12px !important;
  border: none !important;
}
.btnHighlight {
  color: #3d5eff !important;
  background-color: rgba(61, 94, 255, 0.06) !important;
}
.btnActive {
  color: #fff !important;
  background-color: #3d5eff !important;
}
.btnText {
  color: #666 !important;
  height: 36px !important;
  font-size: 12px !important;
  border: none !important;
  background-color: #fff !important;
}


.selectWrapper {
  width: 200px;
  border: none;
  &:hover {
    :global {
      .next-input, .next-select {
        border-color: #3D5EFF;
      }
    }
  }
  :global(.next-input) {
    height: 36px !important;
    border: 1px solid #e4e6ed;
    border-radius: 9px;
  }
}

.inputWrapper {
  width: 200px;
  border: none;
  :global(input) {
    height: 36px !important;
    border: 1px solid #e4e6ed;
    border-radius: 9px;
    &:hover {
      border-color: #3D5EFF;
    }
  }
  :global(.next-input) {
    height: 36px !important;
    border: 1px solid #e4e6ed;
    border-radius: 9px;
  }
}

.datePickerWrapper {
  width: 200px;
  border: none;
  &:hover {
    :global {
      .next-input, .next-select {
        border-color: #3D5EFF;
      }
    }
  }
  :global(.next-input) {
    height: 36px !important;
    border: 1px solid #e4e6ed;
    border-radius: 9px;
  }
  :global(input) {
    height: 36px !important;
  }
}

.warningInfo {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  color: #666;
  background-color: rgba(61, 94, 255, 0.06);
  border-radius: 9px;
  border-radius: 4px;
  margin-bottom: 24px;

  .warningIcon {
    color: #3d5ffe;
    font-size: 20px;
  }
}
.errorInfo {
  background-color: rgba(255, 0, 0, 0.06);
  .warningIcon {
    color: #ff0907;
  }
}

.subTitle {
  line-height: 18px;
  font-size: 12px;
  color: #3d3d3d;
  margin-right: 6px;
}

.flexRow {
  display: flex;
  align-items: center;
}

.popupContainer {
  position: absolute;
  left: 0;
  top: 28px;
  z-index: 5;

  width: 280px;
  border-radius: 12px;
  padding: 12px 18px 9px 12px;
  background: #FFFFFF;
  box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.12);
}

.dialogTitle {
  font-size: 16px;
  line-height: 24px;
  color: #111111;
}
.dialogContent {
  font-size: 14px;
  line-height: 20px;
  color: #666666;
}