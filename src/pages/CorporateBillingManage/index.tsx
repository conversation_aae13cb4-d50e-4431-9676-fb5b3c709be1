import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Table, Button, Select, DatePicker, Input, Icon, Dialog, Field, Form, Message, Balloon } from '@alifd/next';
import clsx from 'clsx';
import moment from 'moment';

import { getCorporateBillList, getSalesInvoice, queryEnterpriseSelect } from '@/services';
import { queryCorporateBillListItem, queryCorporateBillListParams } from '@/types';

import GuestConfirmationModal from './components/GuestConfirmationModal';
import BillDetailDrawer from './components/BillDetailDrawer';
import CreateBillDrawer from './components/CreateBillDrawer';
import BillTableHeader from './components/BillTableHeader';
import OverdueOpModal from './components/OverdueOpModal';
import ApplySalesInvoice from './components/ApplySalesInvoice';
import { BillLabelStatus, BillStatusForTableColMap, BillStatusSelect, InvoiceStatusForTableColMap } from './constants';
import { convertBase64ListToPdf, convertFenToYuan, removeUndefinedKeys } from './utils';

import styles from './index.module.scss';
import s from './styles/common.module.scss';

moment.locale('zh-cn');
const renderStatusLabel = (key, status, isInvoice = false) => {
  const item = isInvoice ? (InvoiceStatusForTableColMap?.[status] || {}) : (BillStatusForTableColMap[status]?.[key] || {});
  if (!item?.text) return '--';
  return <div>
    <div
      className={clsx({
        [s.tag]: true,
        [s.tagGreen]: item.status === BillLabelStatus.done,
        [s.tagGray]: item.status === BillLabelStatus.unfinished,
      })}
    >
      {item.text}
    </div>
  </div>
}
const CorporateBillingManage: React.FC = () => {
  const [curBuyerInfo, setCurBuyerInfo] = useState({
    buyerId: '',
    name: '',
  })
  const [confirmModalVisible, setConfirmModalVisible] = useState(false);
  const [applyInvoiceVisible, setApplyInvoiceVisible] = useState(false);
  const [detailDrawerVisible, setDetailDrawerVisible] = useState(false);
  const [currentDetailBill, setCurrentDetailBill] = useState<any>(null);
  const [createBillDrawerVisible, setCreateBillDrawerVisible] = useState(false);
  const [deleteDialogVisible, setDeleteDialogVisible] = useState(false);
  const [overdueOpModalVisible, setOverdueOpModalVisible] = useState(false);

  const [isLoading, setLoading] = useState(false);
  const [enterpriseList, setEnterpriseList] = useState<Array<{label: string, value: number}>>([]);
  const [pageInfo, setPageInfo] = useState({ current: 1, pageSize: 20, total: 0});
  const [searchParams, setSearchParams] = useState<queryCorporateBillListParams>({} as queryCorporateBillListParams);

  const [billList, setBillList] = useState<queryCorporateBillListItem[]>([]);
  const [visibleColumns, setVisibleColumns] = useState<string[]>([]);

  const hasInitRef = useRef(false);

  const searchFields = Field.useField();

  const columns = [
    {
      title: '账单ID',
      dataIndex: 'batchSettleOrderNo',
      lock: 'left',
      width: 170,
    },
    {
      title: '账单名称',
      dataIndex: 'extDTO',
      width: 160,
      cell: val => <>{val?.batchSettleOrderName || '--'}</>
    },
    {
      title: '出账时间',
      dataIndex: 'billDate',
      width: 120,
      align: 'center',
      cell: val => val ? `每月${val}号` : '--'
    },
    {
      title: '预计完款时间',
      dataIndex: 'settleEndDate',
      width: 200,
      align: 'center',
      cell: (val) => moment(val).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '创建人',
      dataIndex: 'creator',
      width: 120,
      align: 'center',
    },
    {
      title: '开票抬头',
      dataIndex: 'partnerName',
      width: 120,
      align: 'center',
    },
    {
      title: '客户确认',
      dataIndex: 'status',
      lock: 'right',
      width: 180,
      cell: (value: any) => {
        return <div className={styles.invoiceContainer}>
          {renderStatusLabel('GuestConfirm', value)}
          <div className={styles.invoiceText} />
        </div>
      }
    },
    {
      title: '平台开票',
      dataIndex: 'salesInvoiceInfoDTO',
      lock: 'right',
      width: 180,
      cell: (value: any) => {
        const mainInvoice = value?.find(item => item?.parentId === 0); // 主票
        const subInvoiceList = value?.filter(item => item?.parentId !== 0 && item?.status === "fileUpload").map(item => item?.invoiceExtDTO?.invoiceNo); // 已开的子票

        return <div className={styles.invoiceContainer}>
          {renderStatusLabel('PlatformInvoicing', mainInvoice?.status, true)}
          <div className={styles.invoiceText}>
            {
              !!subInvoiceList?.length &&
              <Balloon trigger={<div>发票号: {subInvoiceList?.join(',')}</div>} triggerType="hover">
                {subInvoiceList?.join(',')}
              </Balloon>
            }
            {
              !!mainInvoice && <div>开票时间: {moment(mainInvoice?.gmtCreate).format('YYYY-MM-DD')}</div>
            }
          </div>
        </div>
      },
    },
    {
      title: '客户打款',
      dataIndex: 'status',
      lock: 'right',
      width: 180,
      cell: (value: any) => {
        return <div className={styles.invoiceContainer}>
          { renderStatusLabel('CustomerPayment', value)}
          <div className={styles.invoiceText} />
        </div>
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      lock: 'right',
      width: 80,
      cell: (value: string[], index: number, record: any) => {
        const salesInvoiceInfoDTO = record?.salesInvoiceInfoDTO?.find(item => item?.parentId === 0);
        return <div className={styles.operationColumn}>
        {
          record?.status === 'wait_recon' && (
            <Button
              text
              type="primary"
              onClick={() => { handleConfirm(record) }}
            >
              代客确认
            </Button>
          )
        }
        <Button
          text
          type="primary"
          onClick={() => handleViewDetail(record)}
        >
          账单明细
        </Button>
        {
          ((record?.status === 'lock' || record?.status === 'settling' || record?.status === 'done') && !record?.salesInvoiceInfoDTO) && (
            <Button
              text
              type="primary"
              onClick={() => handleApplyInvoice(record)}
            >
              发票申请
            </Button>
          )
        }

        {
          salesInvoiceInfoDTO?.status === 'fileUpload' && (
            <Button
              text
              type="primary"
              onClick={() => downloadInvoice(record)}
            >
              下载发票
            </Button>
          )
        }
        {/* 暂时不透出预期操作和删除账单 */}
        {/* <Button
          text
          type="primary"
          onClick={() => { setOverdueOpModalVisible(true) }}
        >
          逾期操作
        </Button>
        <Button
          text
          type="primary"
          onClick={() => handleDelete()}
        >
          删除账单
        </Button> */}
      </div>
      }
    }
  ]

  // 获取当前应该显示的列
  const filteredColumns = useMemo(() => {
    if (!visibleColumns || visibleColumns.length === 0) {
      return columns;
    }

    // 操作列和锁定列始终保留
    return columns.filter(col =>
      col.lock === 'left' ||
      col.lock === 'right' ||
      col.dataIndex === 'operation' ||
      visibleColumns.includes(col.dataIndex)
    );
  }, [visibleColumns]);

  // 初始化时从localStorage获取已保存的列配置
  useEffect(() => {
    try {
      const storeCols = JSON.parse(localStorage.getItem('qyg_billing_manage_table_keys') || '[]');
      if (storeCols && storeCols.length > 0) {
        setVisibleColumns(storeCols);
      } else {
        // 如果没有保存的配置，默认显示所有非锁定列
        setVisibleColumns(columns.filter(col => !col.lock && col.dataIndex !== 'operation').map(col => col.dataIndex));
      }
    } catch (err) {
      console.log('[CorporateBillingManage]get storage error:', err);
      setVisibleColumns(columns.filter(col => !col.lock && col.dataIndex !== 'operation').map(col => col.dataIndex));
    }
  }, []);

  // 下载发票
  const downloadInvoice = (record: any) => {
    if (!record?.batchSettleOrderNo) {
      Message.error('账单ID为空');
      return;
    }
    if (!searchFields.getValue('buyerId')) {
      Message.error('企业ID为空');
      return;
    }
    getSalesInvoice({
      batchSettleOrderNo: record?.batchSettleOrderNo,
      buyerId: searchFields.getValue('buyerId'),
    }).then(res => {
      if (res?.success) {
        const { data = []} = res;
        convertBase64ListToPdf(data, record?.batchSettleOrderNo);
        Message.success('发票下载成功');
      } else {
        Message.error(res.errorMsg || '发票下载失败，请稍后重试');
      }
    })
  }

  // 代客确认弹窗
  const handleConfirm = (record: any) => {
    setCurrentDetailBill(record);
    setConfirmModalVisible(true);
  };
  const handleApplyInvoice = (record: any) => {
    setCurrentDetailBill(record);
    setApplyInvoiceVisible(true);
  };

  const handleViewDetail = (record: any) => {
    setCurrentDetailBill(record);
    setDetailDrawerVisible(true);
  };

  const handleDetailDrawerClose = () => {
    setDetailDrawerVisible(false);
  };


  const handleSearchData = (values, errors) => {
    if (errors) return;

    const val: queryCorporateBillListParams = removeUndefinedKeys({ ...values }) as queryCorporateBillListParams;

    if (val.billDate) {
      val.billDate =val.billDate.valueOf()
    }

    setSearchParams(val);
    setPageInfo(prev => ({ ...prev, current: 1}));
  }

  // 点击 table 某条删除
  const handleDelete = () => {
    setDeleteDialogVisible(true);
  }
  // 删除弹窗确认
  const handleDelDialogConfirm = () => {
    setDeleteDialogVisible(false);
  }

  const getList = async () => {
    setLoading(true);
    try {
      const list = await getCorporateBillList({
        ...searchParams,
        pageParam: {
          pageSize: pageInfo.pageSize,
          currentPage: pageInfo.current
        }
      });
      setLoading(false);
      setBillList(list?.pageData?.data || []);
      console.log('==== list', list?.pageData?.data)
      setPageInfo({
        current: list?.pageData?.currentPage,
        pageSize: list?.pageData?.pageSize,
        total: list?.pageData?.totalCount,
      });

      const item = enterpriseList.find(i => i.value.toString() === searchParams?.buyerId?.toString());
      (window as any)._curBuyerInfo = item?.label;
      setCurBuyerInfo({
        buyerId: (item?.value || '') as string,
        name: (item?.label || '') as string
      })
    } catch (err) {
      Message.error('账单列表拉取失败，请稍后重试');
      setLoading(false);
    }
  }

  const fetchEnterpriseList = useCallback(async () => {
    try {
      const list = await queryEnterpriseSelect()
      const data = list?.data?.map(item => {
        return {
          label: `【${item?.roleId}】${item?.name}` || '',
          value: item?.roleId || '',
        }
      }) || []
      setLoading(false);
      setEnterpriseList(data);
      hasInitRef.current = true;
      // searchFields.setValue('buyerId', data?.[0]?.value);
      const buyerId = '10000165';
      searchFields.setValue('buyerId', buyerId);
      setSearchParams({
        ...searchParams,
        // buyerId: data?.[0]?.value
        buyerId,
      })

    } catch (err) {
      Message.error('企业信息获取失败，请刷新后重试')
      setLoading(false);
    }
  }, [])

  useEffect(() => {
    setLoading(true);
    fetchEnterpriseList();
  }, []);

  useEffect(() => {
    if (!hasInitRef.current) return;
    getList();

  }, [searchParams, pageInfo.current, pageInfo.pageSize]);

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h2>企业账单</h2>
        <Button
          type="primary"
          className={clsx(s.btnPrimary, s.btnHighlight)}
          onClick={() => setCreateBillDrawerVisible(true)}
        >
          创建账单
        </Button>
      </div>

      <Form field={searchFields} className={styles.filterSection}>
        <Form.Item name="buyerId" required requiredMessage='请选择企业信息'>
          <Select showSearch placeholder="企业ID/名称" className={s.selectWrapper} dataSource={enterpriseList} />
        </Form.Item>
        <Form.Item name="batchSettleOrderNo">
          <Input placeholder="账单ID" className={s.inputWrapper} />
        </Form.Item>
        <Form.Item name="batchSettleName">
          <Input placeholder="账单名称" className={s.inputWrapper} />
        </Form.Item>
        <Form.Item name="partnerName">
          <Input placeholder="账单对应开票抬头" className={s.inputWrapper} />
        </Form.Item>
        <Form.Item name="status">
          <Select dataSource={BillStatusSelect} placeholder="结算状态" className={clsx(s.selectWrapper, styles.settleStatusInput)} hasClear/>
        </Form.Item>
        <Form.Item  name="billDate">
          <DatePicker placeholder="出账时间" className={s.datePickerWrapper} hasClear/>
        </Form.Item>
        <div className={styles.filterActions}>
          <Form.Submit
            type="primary"
            className={s.btnPrimary}
            validate
            onClick={handleSearchData}
            style={{ marginRight: 8 }}
            disabled={isLoading}
          >
            搜索
          </Form.Submit>
          <Form.Reset disabled={isLoading} className={s.btnText} text><Icon type="ashbin" />清除条件</Form.Reset>
        </div>
      </Form>

      <BillTableHeader
        pageInfo={pageInfo}
        setPageInfo={setPageInfo}
        cols={columns.filter(col => !col.lock && col.dataIndex !== 'operation').map(item => { return { value:  item.dataIndex, label: item.title } })}
        onColumnsChange={setVisibleColumns}
      />

      <Table
        dataSource={billList}
        columns={filteredColumns}
        className={s.table}
        loading={isLoading}
      />
      <GuestConfirmationModal
        visible={confirmModalVisible}
        setVisible={setConfirmModalVisible}
        current={currentDetailBill}
        getList={getList}
      />
      <ApplySalesInvoice
        visible={applyInvoiceVisible}
        setVisible={setApplyInvoiceVisible}
        current={currentDetailBill}
        getList={getList}
      />
      <BillDetailDrawer
        visible={detailDrawerVisible}
        current={currentDetailBill}
        onClose={handleDetailDrawerClose}
      />
      <CreateBillDrawer
        visible={createBillDrawerVisible}
        onClose={() => { setCreateBillDrawerVisible(false) }}
        curBuyerInfo={curBuyerInfo}
        enterpriseList={enterpriseList}
        getList={getList}
      />
      <Dialog
        visible={deleteDialogVisible}
        onClose={() => { setDeleteDialogVisible(false) }}
        footer={[
          <Button className={clsx(s.btnPrimary, s.btnActive)} onClick={() => { handleDelDialogConfirm() }}>确认</Button>,
          <Button className={clsx(s.btnPrimary, s.btnHighlight, s.ml8)} onClick={() => { setDeleteDialogVisible(false) }}>取消</Button>
        ]}
      >
        <div className={styles.deleteDialog}>
          <div><Icon type="warning"  style={{ color: "#FFA003", marginRight: "10px" }} /></div>
          <div>
            <div className={s.dialogTitle}>是否确认删除</div>
            <div className={s.dialogContent}>一经删除不可修改！请谨慎操作</div>
          </div>
        </div>
      </Dialog>

      <OverdueOpModal
        visible={overdueOpModalVisible}
        onClose={() => { setOverdueOpModalVisible(false) }}
        onConfirm={() => {
        }}
      />
    </div>
  );
};

export default CorporateBillingManage;
