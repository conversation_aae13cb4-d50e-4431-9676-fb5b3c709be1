export const BillStatus = {
  init: '待出账',
  wait_recon: '待对账',
  lock: '已锁定',
  settling: '结算中',
  done: '结算完成',
}

export const BillStatusSelect = [
  { label: '待出账', value: 'init' },
  { label: '待对账', value: 'wait_recon' },
  { label: '已锁定', value: 'lock' },
  { label: '结算中', value: 'settling' },
  { label: '结算完成', value: 'done' },
]


export enum BillLabelStatus {
  unfinished = 'unfinished',
  done = 'done',
}

export const BillStatusForTableColMap = {
  init: {
    GuestConfirm: {
      status: BillLabelStatus.unfinished,
      text: '',
    },
    PlatformInvoicing: {
      status: BillLabelStatus.unfinished,
      text: '',
    },
    CustomerPayment: {
      status: BillLabelStatus.unfinished,
      text: '',
    }
  },
  wait_recon: {
    GuestConfirm: {
      status: BillLabelStatus.unfinished,
      text: '待确认',
    },
    PlatformInvoicing: {
      status: BillLabelStatus.unfinished,
      text: '',
    },
    CustomerPayment: {
      status: BillLabelStatus.unfinished,
      text: '',
    }
  },
  lock: {
    GuestConfirm: {
      status: BillLabelStatus.done,
      text: '已确认',
    },
    PlatformInvoicing: {
      status: BillLabelStatus.unfinished,
      text: '待开票',
    },
    CustomerPayment: {
      status: BillLabelStatus.unfinished,
      text: '',
    }
  },
  settling: {
    GuestConfirm: {
      status: BillLabelStatus.done,
      text: '已确认',
    },
    PlatformInvoicing: {
      status: BillLabelStatus.done,
      text: '已开票',
    },
    CustomerPayment: {
      status: BillLabelStatus.unfinished,
      text: '待打款',
    }
  },
  done: {
    GuestConfirm: {
      status: BillLabelStatus.done,
      text: '已确认',
    },
    PlatformInvoicing: {
      status: BillLabelStatus.done,
      text: '已开票',
    },
    CustomerPayment: {
      status: BillLabelStatus.done,
      text: '已打款',
    }
  },
}

export const InvoiceStatusForTableColMap = {
  init: {
    status: BillLabelStatus.unfinished,
    text: '待开票',
  },
  process: {
    status: BillLabelStatus.unfinished,
    text: '开票中',
  },
  complete: {
    status: BillLabelStatus.unfinished,
    text: '开票中',
  },
  fileUpload: {
    status: BillLabelStatus.done,
    text: '已开票',
  },
  processFail: {
    status: BillLabelStatus.unfinished,
    text: '开票失败',
  }
}
export const BillDetailStatusMap = {
  init: '待销账',
  process: '处理中',
  done: '销账完成',
}