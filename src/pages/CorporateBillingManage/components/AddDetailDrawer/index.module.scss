.detailDrawer {
  position: relative;
  .textArea {
    width: 1158px;
    margin-top: 6px;
    height: 300px;
  }
  .checkBox {
    margin-top: 24px;
    display: flex;
    align-items: center;
    height: 36px;
    .checkBtn {
      margin-right: 18px;
    }
    .msg {
      display: flex;
      align-items: center;
      margin-right: 8px;
      .icon {
        margin-right: 6px;
      }
    }
  }
  .footer {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    position: absolute;
    bottom: 20px;
    right: 20px;
  }
  .errorContainer {
    margin-top: 8px;
    .errorItem {
      display: inline-block;
      margin-right: 6px;
      color: #ff0000;
      .cpIcon {
        margin-left: 2px;
        cursor: pointer;
      }
    }
  }
}