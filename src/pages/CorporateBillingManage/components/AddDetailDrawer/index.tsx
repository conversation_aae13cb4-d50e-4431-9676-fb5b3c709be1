import React, { useEffect, useState } from 'react';
import { Drawer, Step, Button, Form, Input, Timeline, Icon, Message } from '@alifd/next';
import clsx from 'clsx';
import ClipboardJS from 'clipboard';

import styles from './index.module.scss';
import s from '../../styles/common.module.scss';

interface AddDetailDrawerProps {
  visible: boolean;
  onClose: () => void;
}

const AddDetailDrawer: React.FC<AddDetailDrawerProps> = ({
  visible,
  onClose,
}) => {

  const [billDetailListStr, setBillDetailListStr] = useState('');
  const [errorBillList, setErrorBillList] = useState<string[]>([]);
  const [successBillList, setSuccessBillList] = useState<string[]>([]);
  const [checkTipsShow, setCheckTipsShow] = useState(false);

  const handleCheckDetailList = () => {
    console.log('======billDetailList', billDetailListStr);
    if (!billDetailListStr) {
      Message.error('请输入明细');
      return;
    }
    setCheckTipsShow(true);
    setErrorBillList(billDetailListStr.split(',') || []);
    setSuccessBillList(['123', '456'] || []);
  }

  const handleCopy = () => {
    Message.success('复制成功');
  }

  useEffect(() => {
    const clipCtx = new ClipboardJS(`.${styles.cpIcon}`);

    clipCtx.on('success', () => {
      Message.success('复制成功');
    });

    clipCtx.on('error', () => {
      Message.error('复制失败, 请手动复制');
    });

    return () => {
      clipCtx.destroy();
    };
  }, []);

  const filterErrorBill = () => {
    if (!billDetailListStr) {
      Message.error('明细数据为空，无法剔除错误信息');
      return;
    }
    const curList = billDetailListStr.split(',');
    const filterList = curList.filter(item => !errorBillList.includes(item));
    setBillDetailListStr(filterList.join(','));
    setErrorBillList([]);
    setSuccessBillList([]);
    Message.success('已剔除错误数据');
  }

  const submit = () => {
    if (!billDetailListStr) {
      Message.error('明细数据为空，无法添加');
      return;
    }
    // 请求
    console.log('========', billDetailListStr);
    onClose?.();
  }


  return (
    <Drawer
      visible={visible}
      onClose={onClose}
      width={1200}
      title="添加明细"
      className={styles.detailDrawer}
    >
      <div className={s.warningInfo}>
        <div className={s.warningIcon}><Icon type="prompt" /></div>
        <div>添加明细为从其他账单向当前订单中批量导入订单明细，若无订单明细，请返回账单管理，选择需要导出明细的账单，下载明细</div>
      </div>
      <Input.TextArea className={styles.textArea} value={billDetailListStr} onChange={(e) => setBillDetailListStr(e)} />
      <div className={styles.checkBox}>
        <Button type='primary' className={clsx(s.btnPrimary, s.btnHighlight, styles.checkBtn)} onClick={handleCheckDetailList}>校验</Button>
        {
          checkTipsShow && <>
            <div className={styles.msg}>
              <Icon type="success" size="xs" className={styles.icon} style={{ color: "#1dc11d" }}/>可移动数据{successBillList.length}条
            </div>
            <div className={styles.msg}>
              <Icon type="prompt" size="xs" className={styles.icon} style={{ color: "#ff3333" }}/>错误数据：{errorBillList.length}项
            </div>
            <Button type='primary' text onClick={filterErrorBill}>一键剔除全部错误信息</Button>
          </>
        }
      </div>
      <div className={styles.errorContainer}>
        {
          errorBillList.map(item => <div className={styles.errorItem}>{item}<Icon type="copy" className={styles.cpIcon} size="xs" data-clipboard-text={item} /></div>)
        }
      </div>
      <div className={styles.footer}>
        <div className={s.subTitle}>请先校验信息，确认无误后再进行移动</div>
        <Button type="primary" className={clsx(s.btnPrimary, s.btnHighlight)} onClick={submit}>添加可识别数据</Button>
      </div>
    </Drawer>
  );
};

export default AddDetailDrawer;