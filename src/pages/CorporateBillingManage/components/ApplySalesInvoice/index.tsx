import React, { useEffect, useState } from 'react';
import { Dialog, Input, Message } from '@alifd/next';
import clsx from 'clsx';

import { getCorporateBillItem, applySalesInvoice } from '@/services';

import styles from './index.module.scss';
import s from '../../styles/common.module.scss';


interface ApplySalesInvoiceProps {
  visible: boolean;
  setVisible: (args: boolean) => void;
  current: Record<string, any>;
  getList: () => void;
}

const ApplySalesInvoice: React.FC<ApplySalesInvoiceProps> = ({
  visible,
  setVisible,
  current,
  getList,
}) => {
  const [invoiceInfo, setInvoiceInfo] = useState<Record<string, string>>({});
  const [remark, setRemark] = useState<string>();

  const onConfirm = async () => {
    if (!current?.batchSettleOrderNo) {
      Message.error('批次单号查询失败，请稍后重试');
      return;
    }
    // 点击确认
    const result = await applySalesInvoice({
      batchSettleOrderNo: current.batchSettleOrderNo,
      saleInvoiceMemo: remark || '',
    })

    console.log('======result', result)
    if (result.success) {
      Message.success('申请开票成功');
      setRemark('');
      onClose?.();
      getList();
    } else {
      Message.error(result?.errorMsg || '申请开票失败，请稍后重试');
    }
  };

  const onClose = () => {
    // 点击取消
    setVisible(false);
  }

  useEffect(() => {
    if (!visible) return;
    getCorporateBillItem({
      batchSettleOrderNo: current?.batchSettleOrderNo,
    }).then(res => {
      console.log('=======res', res.data)
      setInvoiceInfo(res?.data?.invoiceInfoDTO || {});
    })
  }, [visible])


  if (!current) return null;


  return (
    <Dialog
      visible={visible}
      onClose={onClose}
      title="发票申请"
      closeMode={["close", "mask"]}
      footer={false}
      className={styles.confirmModal}
    >
      <div className={styles.section}>
        <h3>发票信息</h3>
        <div className={styles.infoGrid}>
          <div className={styles.infoItem}>
            <span className={styles.label}>购方名称</span>
            <span>{invoiceInfo?.partnerName}</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>购方税号</span>
            <span>{invoiceInfo?.partnerTaxNo}</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>购方类型</span>
            <span>{current?.bizType}</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>购方地址</span>
            <span>{invoiceInfo?.invoiceAddress}</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>购方电话</span>
            <span>{invoiceInfo?.invoiceTelephoneNumber}</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>购方银行账号</span>
            <span>{invoiceInfo?.domainNo}</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>邮寄地址</span>
            <span>{invoiceInfo?.invoiceAddress}</span>
          </div>
        </div>
      </div>

      <div className={styles.section}>
        <h3>添加发票备注</h3>
        <Input.TextArea
          placeholder="请输入备注"
          className={styles.textArea}
          style={{ width: '100%' }}
          value={remark}
          onChange={(val) => setRemark(val)}
        />
      </div>

      <div className={styles.footer}>
        <button className={clsx(s.btnPrimary, s.btnHighlight)} onClick={onClose}>取消</button>
        <button className={clsx(s.btnPrimary, s.btnActive)} onClick={onConfirm}>确认</button>
      </div>
    </Dialog>
  );
};

export default ApplySalesInvoice;