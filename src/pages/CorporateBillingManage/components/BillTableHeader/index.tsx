import React, { useEffect, useState } from 'react';
import { Balloon, Button, Checkbox, Icon, Pagination } from '@alifd/next';
import clsx from 'clsx'

import styles from './index.module.scss';
import s from '../../styles/common.module.scss';

interface BillTableHeaderProps {
  cols: Array<{label: string, value: string}>
  pageInfo: { current: number, total: number, pageSize: number }
  setPageInfo: (args: any) => void
  onColumnsChange?: (columns: string[]) => void
}

const BillTableHeader: React.FC<BillTableHeaderProps> = ({ cols, pageInfo, setPageInfo, onColumnsChange }) => {
  const [colsSelectVisible, setColsSelectVisible] = useState(false);
  const [colsSelectValue, setColsSelectValue] = useState<string[]>([]);

  const handlePageChange = (cur: number) => {
    setPageInfo({
      ...pageInfo,
      current: cur
    })
    // 发起请求
  }

  // 初始化时从localStorage读取已保存的列配置
  useEffect(() => {
    try {
      const storeCols = JSON.parse(localStorage.getItem('qyg_billing_manage_table_keys') || '[]');
      setColsSelectValue(storeCols.length > 0 ? storeCols : cols.map(item => item.value));
    } catch (err) {
      console.log('[BillTableHeader]get storage error:', err);
      setColsSelectValue(cols.map(item => item.value));
    }
  }, []);

  // 当选择弹窗打开时，重新从localStorage读取配置
  useEffect(() => {
    if (!colsSelectVisible) return;
    try {
      const storeCols = JSON.parse(localStorage.getItem('qyg_billing_manage_table_keys') || '[]');
      setColsSelectValue(storeCols.length > 0 ? storeCols : colsSelectValue);
    } catch (err) {
      console.log('[BillTableHeader]get storage error:', err);
    }
  }, [colsSelectVisible])

  const handleStoreCols = () => {
    try {
      // 至少要选择一列
      if (colsSelectValue.length === 0) {
        setColsSelectValue([cols[0].value]);
        localStorage.setItem('qyg_billing_manage_table_keys', JSON.stringify([cols[0].value]));
        if (onColumnsChange) {
          onColumnsChange([cols[0].value]);
        }
      } else {
        localStorage.setItem('qyg_billing_manage_table_keys', JSON.stringify(colsSelectValue));
        if (onColumnsChange) {
          onColumnsChange(colsSelectValue);
        }
      }
      setColsSelectVisible(false);
    } catch (err) {
      console.log('[BillTableHeader]set storage error:', err);
    }
  }

  // 当选择项变化时处理
  const handleColsChange = (values: string[]) => {
    setColsSelectValue(values);
  }

  return <div className={styles.billHeader}>
    <div className={styles.selectRowOpContainer}>
      <Balloon
        className={s.popupContainer}
        visible={colsSelectVisible}
        closable={false}
        onVisibleChange={(val) => { setColsSelectVisible(val) }}
        trigger={<div className={styles.selectRowOp}>选择展示项<Icon type="arrow-down" size="xs" /></div>}
        triggerType="click"
      >
        {
          cols && cols.length && <div className={clsx(styles.selectBox)}>
            <Checkbox.Group
              value={colsSelectValue}
              onChange={(v: string[]) => { handleColsChange(v) }}
            >
              {
                cols.map((item, idx) => <Checkbox key={item.value} className={styles.checkboxItem} value={item.value}> {item.label} </Checkbox>)
              }
            </Checkbox.Group>
            <div className={styles.selectBtnBox}>
              <Button className={clsx(s.btnPrimary, s.btnActive)} onClick={() => { handleStoreCols() }}>确定</Button>
            </div>
          </div>
        }
      </Balloon>
    </div>
    <div className={styles.total}>共 {pageInfo.total} 项数据</div>
    <div className={styles.page}>
      <Pagination
        type="simple"
        shape="arrow-only"
        onChange={(cur) => { handlePageChange(cur) }}
        {...pageInfo}
      />
    </div>
  </div>
}

export default BillTableHeader