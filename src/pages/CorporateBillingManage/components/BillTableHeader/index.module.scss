.billHeader {
  display: flex;
  align-items: center;
  height: 40px;
  justify-content: flex-end;
  font-size: 12px;

  :global(.next-pagination-display) {
    min-width: 25px;
    margin: 0;
  }

  .selectRowOpContainer {
    position: relative;
    .selectRowOp {
      color: #666;
      margin-right: 12px;
      cursor: pointer;
    }
  }

  .total {
    color: #999999;
    margin-right: 12px;
  }

  .page {
    :global(.next-btn:disabled) {
      background-color: #fff;
    }
    :global(.next-pagination-item) {
      border: none;
    }
  }
}

.selectBox {
  .checkboxItem {
    display: inline-block;
    margin-bottom: 10px;
    width: 100px;
  }

  .selectBtnBox {
    display: flex;
    justify-content: flex-end;
  }
}