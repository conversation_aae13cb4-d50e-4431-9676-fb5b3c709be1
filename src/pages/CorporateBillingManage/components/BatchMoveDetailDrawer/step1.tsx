import React from 'react';
import { Drawer, Step, Button, Form, Input, Timeline, Icon } from '@alifd/next';
import clsx from 'clsx';

import styles from './index.module.scss';
import s from '../../styles/common.module.scss';

interface Step1Props {
}

const Step1: React.FC<Step1Props> = ({}) => {
  const handleDownload = () => {
    console.log('======handleDownload');
  }

  return (
    <div>
      <div className={styles.subTitle}>下载账单明细，与客户确认需要移动的订单</div>
      <Button style={{ marginTop: 6 }} type='primary' className={clsx(s.btnPrimary, s.btnHighlight)} onClick={handleDownload}><Icon type="download" />下载账单明细</Button>
    </div>
  );
};

export default Step1;