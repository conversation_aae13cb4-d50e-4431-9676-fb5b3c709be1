import React, { useEffect, useState } from 'react';
import { Drawer, Step, Button, Form, Input, Timeline, Icon, Message } from '@alifd/next';
import clsx from 'clsx';
import ClipboardJS from 'clipboard';

import styles from './index.module.scss';
import s from '../../styles/common.module.scss';

interface Step1Props {
  setBillDetailListStr: (args: string) => void;
  billDetailListStr: string;
}

const Step3: React.FC<Step1Props> = ({ billDetailListStr, setBillDetailListStr }) => {
  const [errorBillList, setErrorBillList] = useState<string[]>([]);
  const [successBillList, setSuccessBillList] = useState<string[]>([]);
  const [checkTipsShow, setCheckTipsShow] = useState(false);
  const handleCheckDetailList = () => {
    console.log('======billDetailList', billDetailListStr);
    if (!billDetailListStr) {
      Message.error('请输入明细');
      return;
    }
    setCheckTipsShow(true);
    setErrorBillList(billDetailListStr.split(',') || []);
    setSuccessBillList(['123', '456'] || []);
  }

  useEffect(() => {
    const clipCtx = new ClipboardJS(`.${styles.cpIcon}`);

    clipCtx.on('success', () => {
      Message.success('复制成功');
    });

    clipCtx.on('error', () => {
      Message.error('复制失败, 请手动复制');
    });

    return () => {
      clipCtx.destroy();
    };
  }, []);

  const filterErrorBill = () => {
    if (!billDetailListStr) {
      Message.error('明细数据为空，无法剔除错误信息');
      return;
    }
    const curList = billDetailListStr.split(',');
    const filterList = curList.filter(item => !errorBillList.includes(item));
    setBillDetailListStr(filterList.join(','));
    setErrorBillList([]);
    setSuccessBillList([]);
    Message.success('已剔除错误数据');
  }


  return (
    <div className={styles.step3}>
      <div className={styles.subTitle}>导出EXCEL数据时，请将订单号用逗号","分隔</div>
      <Input.TextArea
        placeholder="添加移动明细时，请将订单用逗号分隔，例如：xxxxxxxxxxxxx，xxxxxxxxxxxxx，xxxxxxxxxxxxx,........."
        value={billDetailListStr}
        onChange={(e) => setBillDetailListStr(e)}
        className={styles.textArea}
      />

      <div className={styles.checkBox}>
        <Button type='primary' className={clsx(s.btnPrimary, s.btnHighlight)} onClick={handleCheckDetailList}>校验</Button>
        {
          checkTipsShow && <>
            <div className={clsx(styles.msg, s.ml8)}>
              <Icon type="success" size="xs" className={styles.icon} style={{ color: "#1dc11d" }}/>可移动数据{successBillList.length}条
            </div>
            <div className={styles.msg}>
              <Icon type="prompt" size="xs" className={styles.icon} style={{ color: "#ff3333" }}/>错误数据：{errorBillList.length}条
            </div>
            <Button type='primary' text onClick={filterErrorBill}>一键剔除全部错误信息</Button>
          </>
        }
      </div>

      <div className={styles.errorContainer}>
        {
          errorBillList.map(item => <div className={styles.errorItem}>{item}<Icon type="copy" className={styles.cpIcon} size="xs" data-clipboard-text={item} /></div>)
        }
      </div>
    </div>
  );
};

export default Step3;