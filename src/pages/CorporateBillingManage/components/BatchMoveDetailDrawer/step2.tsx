import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON>, <PERSON> } from '@alifd/next';
import clsx from 'clsx';

import CreateBillDrawer from '../CreateBillDrawer';

import styles from './index.module.scss';
import s from '../../styles/common.module.scss';

interface Step1Props {
  billList: Array<{label: string, value: string}>
}

const AddIcon = () => <svg xmlns="http://www.w3.org/2000/svg" fill="none" version="1.1" width="12" height="12" viewBox="0 0 12 12"><g><g><path d="M5.999984264373779,0.9999995231628418C3.2499842643737793,0.9999995231628418,0.9999842643737793,3.249999523162842,0.9999842643737793,5.999999523162842C0.9999842643737793,8.750009523162841,3.2499842643737793,10.999999523162842,5.999984264373779,10.999999523162842C8.749994264373779,10.999999523162842,10.99998426437378,8.750009523162841,10.99998426437378,5.999999523162842C10.99998426437378,3.249999523162842,8.749994264373779,0.9999995231628418,5.999984264373779,0.9999995231628418ZM5.999984264373779,10.250009523162841C3.6499942643737793,10.250009523162841,1.7499842643737793,8.349999523162841,1.7499842643737793,5.999999523162842C1.7499842643737793,3.6500095231628418,3.6499942643737793,1.7499995231628418,5.999984264373779,1.7499995231628418C8.349984264373779,1.7499995231628418,10.249994264373779,3.6500095231628418,10.249994264373779,5.999999523162842C10.249994264373779,8.349999523162841,8.349984264373779,10.250009523162841,5.999984264373779,10.250009523162841ZM6.499994264373779,5.750009523162841L8.374994264373779,5.750009523162841C8.574984264373779,5.750009523162841,8.749994264373779,5.925019523162842,8.749994264373779,6.125009523162841C8.749994264373779,6.324999523162842,8.59999426437378,6.474999523162841,8.39998426437378,6.500009523162841L6.499994264373779,6.500009523162841L6.499994264373779,8.375009523162841C6.499994264373779,8.574999523162841,6.324994264373779,8.750009523162841,6.124994264373779,8.750009523162841C5.924984264373779,8.750009523162841,5.77498426437378,8.600009523162843,5.749994264373779,8.399999523162842L5.749994264373779,6.500009523162841L3.6249842643737793,6.500009523162841C3.424994264373779,6.500009523162841,3.2499842643737793,6.325009523162842,3.2499842643737793,6.125009523162841C3.2499842643737793,5.924999523162842,3.399984264373779,5.774999523162842,3.5999842643737794,5.750009523162841L5.749994264373779,5.750009523162841L5.749994264373779,3.624999523162842C5.749994264373779,3.4250095231628417,5.925004264373779,3.249999523162842,6.124994264373779,3.249999523162842C6.3249842643737795,3.249999523162842,6.474984264373779,3.3999995231628417,6.499994264373779,3.599999523162842L6.499994264373779,5.750009523162841Z" fill-rule="evenodd" fill="#3D5EFF" fill-opacity="1"/></g></g></svg>

const Step2: React.FC<Step1Props> = ({ billList }) => {
  const [createBillDrawerVisible, setCreateBillDrawerVisible] = useState(false);

  return (
    <>
      <div className={styles.step2}>
        <div className={s.flexRow}>
          <div className={styles.subTitle}>只能移向小二创建订单或创建新账单</div>
          <Button type="primary" text className={clsx(s.flexRow, s.ml8)}  onClick={() => setCreateBillDrawerVisible(true)}><AddIcon />新建账单</Button>
        </div>
        <Select
          placeholder="请选择"
          showSearch
          hasClear
          className={styles.select}
          dataSource={billList}
        />
      </div>
      <CreateBillDrawer
        visible={createBillDrawerVisible}
        onClose={() => { setCreateBillDrawerVisible(false) }}
      />
    </>
  );
};

export default Step2;