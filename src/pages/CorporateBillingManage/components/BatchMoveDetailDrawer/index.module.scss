.detailDrawer {
  position: relative;
  :global {
    .next-drawer-body {
      padding: 24px;
    }
  }
}

.step2 {
  .select {
    width: 340px;
    border: none;
    margin-top: 6px;
    &:hover {
      :global {
        .next-input, .next-select {
          border-color: #3D5EFF;
        }
      }
    }
    :global(.next-input) {
      height: 36px;
      border: 1px solid #e4e6ed;
      border-radius: 9px;
    }
    :global(input) {
      height: 36px;
    }
  }
}

.step3 {
  .textArea {
    width: 1130px;
    margin-top: 6px;
    height: 300px;
  }
  :global(.next-input-textarea) {
    border: 1px solid #e4e6ed;
    border-radius: 9px;
  }

  .errorContainer {
    .errorItem {
      display: inline-block;
      margin-right: 6px;
      color: #ff0000;
      .cpIcon {
        margin-left: 2px;
      }
    }
  }
}

.checkBox {
  margin-top: 8px;
  display: flex;
  align-items: center;
  height: 36px;
  .checkBtn {
    margin-right: 18px;
  }
  .msg {
    display: flex;
    align-items: center;
    margin-right: 8px;
    .icon {
      margin-right: 6px;
    }
  }
}

.footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  position: absolute;
  bottom: 20px;
  right: 20px;
}

.subTitle {
  font-size: 12px;
}