import React, { useEffect, useState } from 'react';
import { Drawer, Step, Button, Form, Input, Timeline, Message } from '@alifd/next';

import Step1 from './step1';
import Step2 from './step2';
import Step3 from './step3';

import styles from './index.module.scss';
import s from '../../styles/common.module.scss';

import clsx from 'clsx';

interface BatchMoveDetailDrawerProps {
  visible: boolean;
  onClose: () => void;
}

const BatchMoveDetailDrawer: React.FC<BatchMoveDetailDrawerProps> = ({
  visible,
  onClose,
}) => {

  const [billDetailListStr, setBillDetailListStr] = useState('');
  const [billList, setBillList] = useState([]);
  const submit = () => {
    if (!billDetailListStr) {
      Message.error('明细数据为空，无法移动');
      return;
    }
    // 请求
    console.log('========', billDetailListStr);
    onClose?.();
  }

  useEffect(() => {
    // 拉账单列表
    if (visible) {
      setBillList([]);
    }
  }, []);


  return (
    <Drawer
      visible={visible}
      onClose={onClose}
      width={1200}
      title="批量转移明细 "
      className={styles.detailDrawer}
    >
      <Timeline>
        <Timeline.Item
          title={"第一步：下载账单明细"}
          content={<Step1 />}
          state="process"
        />
        <Timeline.Item
          title={"第二步：选择移动账单"}
          content={<Step2 billList={billList}/>}
          state="process"
        />
        <Timeline.Item
          title={"第三步：添加移动明细"}
          content={<Step3 billDetailListStr={billDetailListStr} setBillDetailListStr={setBillDetailListStr}/>}
          state="process"
        />
        <Timeline.Item
          // state="process"
          dot={<div></div>}
        />
      </Timeline>
      <div className={styles.footer}>
        <div className={s.subTitle}>请先交验信息，确认无误后再进行移动</div>
        <Button type="primary" className={clsx(s.btnPrimary, s.btnHighlight)} onClick={submit}>移动可识别数据</Button>
      </div>
    </Drawer>
  );
};

export default BatchMoveDetailDrawer;