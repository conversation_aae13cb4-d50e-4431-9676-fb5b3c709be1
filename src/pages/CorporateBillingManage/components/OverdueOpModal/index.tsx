import React, { useEffect, useState } from 'react';
import { Button, Dialog, Input, Message, Table } from '@alifd/next';
import clsx from 'clsx';

import styles from './index.module.scss';
import s from '../../styles/common.module.scss';

interface OverdueOpModalProps {
  visible: boolean;
  onClose: () => void;
  onConfirm: () => void;
}

const columns = [
  {
    title: '操作时间',
    dataIndex: 'time',
    width: 180,
  },
  {
    title: '操作人',
    dataIndex: 'operator',
    width: 80,
  },
  {
    title: '操作记录',
    dataIndex: 'info',
  },
];

const OverdueOpModal: React.FC<OverdueOpModalProps> = ({
  visible,
  onClose,
  onConfirm
}) => {
  const [opHistoryList, setOpHistoryList] = useState<Array<{operator: string, time: string, info: string}>>([]);
  const [opInfo, setOpInfo] = useState<string|number>('');

  useEffect(() => {
    if (!visible) return;

    // fetch Data

    setOpHistoryList([{
      operator: '啦啦啦',
      time: '2023-01-12 12:00:11',
      info: '看就是拉开距离卡金德拉克撒娇的卢卡斯'
    }, {
      operator: '哈哈哈',
      time: '2023-01-12 11:00:11',
      info: '看看坎坎坷坷看坎坎坷坷超级长看看坎坎坷坷看坎坎坷坷超级长看看坎坎坷坷看坎坎坷坷超级长看看坎坎坷坷看坎坎坷坷超级长看看坎坎坷坷看坎坎坷坷超级长看看坎坎坷坷看坎坎坷坷超级长看看坎坎坷坷看坎坎坷坷超级长看看坎坎坷坷看坎坎坷坷超级长看看坎坎坷坷看坎坎坷坷超级长看看坎坎坷坷看坎坎坷坷超级长'
    }])
  }, [visible]);

  const handleAddOpInfo = () => {
    if (!opInfo) {
      Message.error('请输入操作记录');
      return;
    }
    console.log('=====', opInfo);
    Message.success('记录添加成功');
    setOpInfo('');
    // 重新拉取数据
  }

  return (
    <Dialog
      visible={visible}
      onClose={onClose}
      title="逾期操作记录"
      closeMode={["close", "mask"]}
      footer={false}
      className={styles.confirmModal}
    >
      <div className={styles.infoGrid}>
        <div className={styles.infoItem}>
          <span className={styles.label}>操作人</span>
          <span>某人(123010)</span>
        </div>
        <div className={styles.infoItem}>
          <span className={styles.label}>操作时间</span>
          <span>2023-01-12 12:00:11</span>
        </div>
      </div>
      <h3>添加操作记录</h3>
      <div className={styles.addBox}>
        <Input placeholder='请输入' className={clsx(s.inputWrapper, styles.input)} value={opInfo} onChange={(e) => setOpInfo(e)}/>
        <Button type="primary" className={s.btnPrimary} onClick={handleAddOpInfo}>
          添加
        </Button>
      </div>
      <h3>历史操作记录</h3>
      <Table
        columns={columns}
        dataSource={opHistoryList}
        className={s.table}
      />
    </Dialog>
  );
};

export default OverdueOpModal;