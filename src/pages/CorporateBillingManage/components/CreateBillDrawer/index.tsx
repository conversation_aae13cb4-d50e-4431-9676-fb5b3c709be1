import React, { useEffect, useState } from 'react';
import { Drawer, Select, Form, Input, DatePicker, Field, Message } from '@alifd/next';
import clsx from 'clsx';
import moment from 'moment';

import { createBatchOrder, getUserInfo } from '@/services';

import styles from './index.module.scss';
import s from '../../styles/common.module.scss';

interface CreateBillDrawerProps {
  visible: boolean;
  onClose: () => void;
  curBuyerInfo: {
    buyerId: string;
    name: string;
  }
  enterpriseList: Array<{label: string, value: number}>;
  getList: () => void;
}

const formItemLayout = {
  labelCol: {
    fixedSpan: 6
  },
};

const CreateBillDrawer: React.FC<CreateBillDrawerProps> = ({
  visible,
  onClose,
  curBuyerInfo,
  enterpriseList,
  getList,
}) => {
  const fields = Field.useField();
  const [userInfo, setUserInfo] = useState({ creator: '', creatorId: '' });
  const handleCreateBill = (val, err) => {
    if (err) {
      return;
    }
    if (!curBuyerInfo?.buyerId) {
      Message.error('暂未查询到企业信息，请稍后重试');
      return;
    }

    createBatchOrder({
      buyerId: val?.buyerId,
      batchSettleName: val.batchSettleName,
      contractId: val.contractId,
      settleEndDate: moment(val.settleEndDate).set({ hour: 23, minute: 59, second: 59 }).format('YYYY-MM-DD hh:mm:ss'),
      operator: `${userInfo.creator}(${userInfo.creatorId})`
    }).then(res => {
      if (!res.success) {
        Message.error(res.errorMsg);
        return;
      }
      Message.success("新建账单成功，可选择操作-账单明细向该订单添加明细");
      onClose?.();
      getList();
    })
  }

  useEffect(() => {
    getUserInfo().then(res => {
      setUserInfo({
        creator: res.data?.creator,
        creatorId: res.data?.creator_id,
      })
    })
  }, [])

  return <Drawer
    visible={visible}
    onClose={onClose}
    width={540}
    title="创建账单"
    className={styles.createBillDrawer}
  >
    <Form {...formItemLayout} field={fields}>
      <div className={styles.stepTitle}>第一步：基本信息</div>
      <Form.Item label="企业信息" required name="buyerId" requiredMessage='请选择企业'>
        <Select placeholder='请选择'  className={s.selectWrapper} style={{ width: '100%' }} dataSource={enterpriseList}defaultValue={curBuyerInfo?.buyerId} />
      </Form.Item>
      <Form.Item label="账单名称" required labelAlign="left" name="batchSettleName" requiredMessage='请输入账单名称' maxLength={20} >
        <Input placeholder='请输入' className={s.inputWrapper} />
      </Form.Item>
      <Form.Item label="合同ID" required name="contractId" requiredMessage='请输入合同ID'>
        <Input placeholder='请输入' className={s.inputWrapper} />
      </Form.Item>
      <Form.Item label="创建人" required labelAlign="left" name="operator">
        <div className={styles.formItem}>{userInfo.creator}({userInfo.creatorId})</div>
      </Form.Item>
      <Form.Item label="预计完款时间" required name="settleEndDate" requiredMessage='请选择预计完款时间'>
        <DatePicker placeholder='请选择' style={{ width: '100%' }} className={s.datePickerWrapper} />
      </Form.Item>
      <div className={styles.opBtnBox}>
        <Form.Submit validate className={clsx(s.btnPrimary, s.btnHighlight, styles.btn)} onClick={handleCreateBill}>
          确定
        </Form.Submit>
      </div>
    </Form>


  </Drawer>
}

export default CreateBillDrawer