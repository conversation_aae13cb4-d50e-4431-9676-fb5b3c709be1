import React, { useEffect, useState, useMemo, useRef } from 'react';
import { Drawer, Table, Button, Form, Input, Icon, Checkbox, Pagination, Select, Field, Message, Balloon, List } from '@alifd/next';
import clsx from 'clsx';
import moment from 'moment';

import { getCorporateBillDetail } from '@/services';
import { queryCorporateBillDetailItem, queryCorporateBillDetailParams, queryCorporateBillDetailResult } from '@/types';

import AddDetailDrawer from '../AddDetailDrawer';
import BatchMoveDetailDrawer from '../BatchMoveDetailDrawer';
import { convertFenToYuan, removeUndefinedKeys } from '../../utils';

import styles from './index.module.scss';
import s from '../../styles/common.module.scss';
import { BillDetailStatusMap } from '../../constants';
import { getColumns } from './column';


interface BillDetailDrawerProps {
  visible: boolean;
  onClose: () => void;
  current: Record<string, any>
}

const BaseInfoKVMap = {
  "batchSettleOrderNo": "账单ID",
  // "项目iD"
  "contractId": "合同id",
  // "发票抬头"
  "billDate": '出账日',
  "billStartDate": '账单开始时间',
  "billEndDate": '账单结束时间',
  "settleStartDate": '预计完款日',

  // "settleStartDate": '结算开始时间',
  // "gmtModified": '修改时间',
  // "domainType": "主体类型",
  // "billEndDate": '账单结束时间',
  // "contractVersion": "合同版本",
  // "billStartDate": '账单开始时间',
  // "fundFlow": "资金流向",
  // "id": "子批次单",
  // "operatorId": "操作人id",
  // "creator": "创建人",
  // "amount": '账单金额',
  // "partnerName": "交易对象户名",
  // "billCycleType": "账单汇总周期类型",
  // "billDate": '出账日',
  // "settleEndDate": '结算截止时间',
  // "gmtCreate": '创建时间',
  // "domainId": '主体id',
  // "parentId": '父id',
  // "settleCycleType": "结算周期类型",
  // "domainNo": "主体户号",
  // "domainName": "主体户名",
  // "contractId": "合同实例id",
  // "settleCycle": "结算周期类型",
  // "batchSettleOrderNo": "批次结算单号",
  // "billCycle": "账单汇总周期",
  // "status": "状态"
}
const BaseInfoTimeKey = ["settleStartDate", "billStartDate", "billEndDate", "settleEndDate"]
const Price = ({ price }) => {
  if (!price) {
    return <div className={styles.priceContainer}>
      <span className={styles.integer}>--</span>
    </div>
  }
  const priceFormat = Number.isInteger(price) ? Number(price).toFixed(2) : price;
  const [integerPart, decimalPart] = priceFormat.toString().split('.');
  if (Number.isNaN(Number(integerPart)) || Number.isNaN(Number(decimalPart))) {
    return <div className={styles.priceContainer}>
      <span className={styles.integer}>--</span>
    </div>
  }
  return (
    <div className={styles.priceContainer}>
      <span className={styles.currency}>¥</span>
      <span className={styles.integer}>{integerPart}</span>
      <span className={styles.currency}>.</span>
      <span className={styles.decimal}>{decimalPart}</span>
    </div>
  );
}
const BillDetailDrawer: React.FC<BillDetailDrawerProps> = ({
  visible,
  onClose,
  current,
}) => {

  const columns = useMemo(() => {
    return getColumns(current);
  }, []);

  // 为每一列添加唯一的key标识
  const columnsWithKeys = useMemo(() => {
    return columns.map((col, index) => ({
      ...col,
      columnKey: `col_${index}_${col.dataIndex || 'noIndex'}_${col.title.replace(/\s+/g, '_')}`
    }));
  }, [columns]);

  const [detailList, setDetailList] = useState<queryCorporateBillDetailItem[]>([]);

  const [colsSelectVisible, setColsSelectVisible] = useState(false);
  const [batchMoveDrawVisible, setBatchMoveDrawVisible] = useState(false);
  const [addDetailDrawerVisible, setAddDetailDrawerVisible] = useState(false);
  const [moveDetailPopupVisible, setMoveDetailPopupVisible] = useState(false);

  const [selectRows, setSelectRows] = useState<string[]>([]);
  const [tempColsSelectValue, setTempColsSelectValue] = useState<string[]>([]);
  const [filteredColumns, setFilteredColumns] = useState(columnsWithKeys);
  const [moveSearchInputVal, setMoveSearchInputVal] = useState('');
  const [filteredBillListData, setFilteredBillListData] = useState([]);
  const [moveSelectBill, setMoveSelectBill] = useState('');

  const [isLoading, setLoading] = useState(false);

  const [searchParams, setSearchParams] = useState<queryCorporateBillDetailParams>({});
  const [pageInfo, setPageInfo] = useState({
    current: 1,
    total: 0,
    pageSize: 20,
  });

  const searchFields = Field.useField();

  const getList = async (batchSettleOrderNo) => {
    setLoading(true);
    try {
      const list = await getCorporateBillDetail({
        ...searchParams,
        batchSettleOrderNo,
        pageParam: {
          pageSize: pageInfo.pageSize,
          currentPage: pageInfo.current
        }
      })

      setLoading(false);
      console.log('=====MINGXI LIST', list?.pageData?.data)
      setDetailList(list?.pageData?.data || []);
      setPageInfo({
        current: list?.pageData?.currentPage,
        pageSize: list?.pageData?.pageSize,
        total: list?.pageData?.totalCount,
      });
    } catch (err) {
      Message.error('明细列表拉取失败，请稍后重试');
      setLoading(false);
    }
  }

  useEffect(() => {
    if (!visible) return;
    console.log('=====current', current);
    getList(current?.batchSettleOrderNo);
  }, [visible, searchParams, pageInfo.current, pageInfo.pageSize])


  useEffect(() => {
    // 拉取可移动批次单列表
    // setFilteredBillListData([]);
  }, [moveSearchInputVal]);

  useEffect(() => {
    // fetch bill/table data
  }, [])

  const handleSearch = (values, errors) => {
    if (errors) return;
    const val: queryCorporateBillDetailParams = removeUndefinedKeys({ ...values });
    if (val.id) {
      val.ids = [val.id]
      delete val.id;
    }
    setSearchParams(val);
    setPageInfo(prev => ({ ...prev, current: 1}));
  }

  // 删除明细
  const handleDeleteDetail = () => {
    if (!selectRows.length) {
      Message.warning('请选择后再删除');
      return;
    }

    Message.success('删除成功');
    // 重新请求
    setSelectRows([]);
  }

  const handleMoveDetail = () => {
    if (!moveSelectBill) {
      Message.warning('请选择要移动到的账单');
      return;
    }

    Message.success('移动成功');
    setMoveDetailPopupVisible(false);
    setSelectRows([]);
  }

  const handlePageChange = (cur: number) => {
    setPageInfo({
      ...pageInfo,
      current: cur
    })
    // 发起请求
  }

  const handleMove = (val) => {
    if (!selectRows.length) {
      Message.warning('请选择后再进行移动操作');
      return;
    }
    setMoveDetailPopupVisible(val);
  }

  const handleStoreCols = () => {
    try {
      // 至少要选择一列
      if (tempColsSelectValue.length === 0) {
        // 默认选择第一列
        setTempColsSelectValue([columnsWithKeys[0].columnKey]);

        // 过滤要展示的列
        const newFilteredColumns = columnsWithKeys.filter(col =>
          col.lock === 'left' || col.lock === 'right' || col.columnKey === columnsWithKeys[0].columnKey
        );

        setFilteredColumns(newFilteredColumns);

        // 存储至localStorage
        localStorage.setItem('qyg_bill_detail_table_keys', JSON.stringify([columnsWithKeys[0].columnKey]));
      } else {
        // 过滤要展示的列
        const newFilteredColumns = columnsWithKeys.filter(col =>
          col.lock === 'left' || col.lock === 'right' || tempColsSelectValue.includes(col.columnKey)
        );

        setFilteredColumns(newFilteredColumns);

        // 存储至localStorage
        localStorage.setItem('qyg_bill_detail_table_keys', JSON.stringify(tempColsSelectValue));
      }

      // 关闭选择弹窗
      setColsSelectVisible(false);
    } catch (err) {
      console.log('[BillDetailDrawer]set storage error:', err);
    }
  }

  // 初始化时从localStorage读取已保存的列配置
  useEffect(() => {
    try {
      const storeCols = JSON.parse(localStorage.getItem('qyg_bill_detail_table_keys') || '[]');
      // 如果有保存的配置，应用它
      if (storeCols.length > 0) {
        setTempColsSelectValue(storeCols);

        // 过滤要展示的列
        const newFilteredColumns = columnsWithKeys.filter(col =>
          col.lock === 'left' || col.lock === 'right' || storeCols.includes(col.columnKey)
        );

        setFilteredColumns(newFilteredColumns);
      } else {
        // 没有配置时，默认展示所有列
        const defaultColKeys = columnsWithKeys
          .filter(col => !(col.lock === 'left' || col.lock === 'right'))
          .map(col => col.columnKey);

        setTempColsSelectValue(defaultColKeys);
      }
    } catch (err) {
      console.log('[BillDetailDrawer]get storage error:', err);
    }
  }, []);

  // 当选择弹窗打开时，重新从localStorage读取配置
  useEffect(() => {
    if (!colsSelectVisible) return;
    try {
      const storeCols = JSON.parse(localStorage.getItem('qyg_bill_detail_table_keys') || '[]');
      setTempColsSelectValue(storeCols.length > 0 ? storeCols : tempColsSelectValue);
    } catch (err) {
      console.log('[BillDetailDrawer]get storage error:', err);
    }
  }, [colsSelectVisible]);


  if (!current) return null;

  return (
    <Drawer
      visible={visible}
      onClose={onClose}
      width={1200}
      title="账单明细"
      className={styles.billDetailDrawer}
    >
      <div className={styles.basicInfo}>
        <h3>基本信息</h3>
        <div className={styles.infoGrid}>
          <div className={styles.infoItem}>
            <span className={styles.label}>客户信息</span>
            <span>{(window as any)?._curBuyerInfo}</span>
          </div>
          {
            Object.keys(BaseInfoKVMap).map((key, idx) => {
              return <div className={styles.infoItem} key={idx}>
              <span className={styles.label}>{BaseInfoKVMap[key]}</span>
              {
                key === 'billDate' ? <span className={styles.labelContent}>{ current[key] ? `每月${current[key]}日` : '--' }</span> :
                BaseInfoTimeKey.includes(key) ? <span className={styles.labelContent}>{moment(current[key]).format('YYYY-MM-DD')}</span> : <span className={styles.labelContent}>{current[key]}</span>
              }
            </div>
            })
          }
        </div>
      </div>

      <div className={clsx(styles.basicInfo, styles.billAmount)}>
        <h3>账单金额</h3>
        <div className={styles.amountInfo}>
          <div className={styles.amountItem}>
            <span className={styles.label}>总金额（含税）</span>
            <Price price={current?.amount}/>
          </div>
          {/* <div className={styles.amountItem}>
            <span className={styles.label}>总金额（不含税）</span>
            <Price price={current?.amountTax}/>
          </div>
          <div className={styles.amountItem}>
            <span className={styles.label} style={{ marginRight: 40 }}>税费</span>
            <Price price={current?.amounttaxTx}/>
          </div> */}
        </div>
      </div>

      <div className={styles.orderList}>
        <div className={styles.orderHeader}>
          <Form className={styles.searchSection} field={searchFields}>
            <Form.Item name="auctionName">
              <Input placeholder="商品名称" className={s.inputWrapper} />
            </Form.Item>
            <Form.Item name="tpOrderId">
              <Input placeholder="采购子订单ID" className={s.inputWrapper} />
            </Form.Item>
            <Form.Item name="parentTpOrderId">
              <Input placeholder="采购父订单ID" className={s.inputWrapper} />
            </Form.Item>
            <Form.Item name="id">
              <Input placeholder="账单明细ID" className={s.inputWrapper} />
            </Form.Item>
            <Form.Submit
              type="primary"
              className={s.btnPrimary}
              validate
              onClick={handleSearch}
              style={{ marginRight: 8 }}
            >
              搜索
            </Form.Submit>
            <Form.Reset className={s.btnText} text><Icon type="ashbin" />清除条件</Form.Reset>
          </Form>
        </div>

        <div className={styles.detailHeader}>
          <div className={styles.mainOp}>
            <div className={styles.hasChoose}>已选{selectRows.length}</div>
            <div onClick={handleDeleteDetail}>删除</div>
            <Balloon
              className={clsx(s.popupContainer, styles.popupContainer)}
              visible={moveDetailPopupVisible}
              closable={false}
              onVisibleChange={(val) => { handleMove(val) }}
              trigger={<div>移动</div>}
              triggerType="click"
              align='br'
            >
              <Input
                className={clsx(s.inputWrapper, styles.searchInput)}
                placeholder="输入内容搜索"
                value={moveSearchInputVal}
                onChange={(val: string) => setMoveSearchInputVal(val)}
              />
              <div className={styles.moveList}>
                <List
                  dataSource={filteredBillListData}
                  renderItem={(item: {label: string, val: string}) => (
                    <List.Item
                      onClick={() => setMoveSelectBill(item.val)}
                      style={{ padding: '8px 16px', cursor: 'pointer' }}
                      className={clsx({ [styles.activeListItem]: moveSelectBill === item.val })}
                    >
                      {item.label}
                    </List.Item>
                  )}
                />
              </div>
              <div className={styles.btnBox}>
                <Button className={clsx(s.btnPrimary, s.btnActive)} onClick={handleMoveDetail}>移动</Button>
              </div>
            </Balloon>
          </div>

          <div className={styles.subOp}>
            <Button type="primary" className={clsx(s.btnPrimary, s.btnHighlight)} onClick={() => { setAddDetailDrawerVisible(true) }}>添加明细</Button>
            <Button type="primary" className={clsx(s.btnPrimary, s.btnHighlight, s.ml8)} onClick={() => { setBatchMoveDrawVisible(true) }}>批量转移明细</Button>
            <div className={clsx(s.ml8, styles.selectRowOpContainer)}>
              <Balloon
                className={s.popupContainer}
                visible={colsSelectVisible}
                closable={false}
                onVisibleChange={(val) => { setColsSelectVisible(val) }}
                trigger={<div className={styles.selectRowOp}>选择展示项<Icon type="arrow-down" size="xs" /></div>}
                triggerType="click"
              >
                <div className={styles.selectBox}>
                  <Checkbox.Group
                    value={tempColsSelectValue}
                    onChange={(v: string[]) => { setTempColsSelectValue(v) }}
                    aria-labelledby="groupId"
                  >
                    {
                      columnsWithKeys.map((item) => {
                        if (item.lock === 'left' || item.lock === 'right') {
                          return null
                        }
                        return <Checkbox
                          key={item.columnKey}
                          className={styles.checkboxItem}
                          value={item.columnKey}
                        >
                          {item.title}
                        </Checkbox>
                      })
                    }
                  </Checkbox.Group>
                  <div className={styles.selectBtnBox}>
                    <Button className={clsx(s.btnPrimary, s.btnActive)} onClick={handleStoreCols}>确定</Button>
                  </div>
                </div>
              </Balloon>
            </div>
            <div className={styles.total}>共 {pageInfo.total} 项数据</div>
            <div className={styles.page}>
              <Pagination
                type="simple"
                shape="arrow-only"
                onChange={(cur) => { handlePageChange(cur) }}
                {...pageInfo}
              />
            </div>
          </div>
        </div>

        <Table
          primaryKey="bizOrderNo"
          dataSource={detailList}
          columns={filteredColumns}
          className={s.table}
          loading={isLoading}
          rowSelection={{
            onChange: () => {},
            onSelect: (selected, record, records) => {
              const list = records.map(item => item.bizOrderNo);
              setSelectRows(list as string[]);
            },
            onSelectAll: (selected, records) => {
              const list = records.map(item => item.bizOrderNo);
              setSelectRows(list as string[]);
            },
            selectedRowKeys: selectRows
          }}
        />
      </div>
      <BatchMoveDetailDrawer
        visible={batchMoveDrawVisible}
        onClose={() => { setBatchMoveDrawVisible(false) }}
      />
      <AddDetailDrawer
        visible={addDetailDrawerVisible}
        onClose={() => { setAddDetailDrawerVisible(false) }}
      />
    </Drawer>
  );
};

export default BillDetailDrawer;