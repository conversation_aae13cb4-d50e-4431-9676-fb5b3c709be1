import React from 'react';

import styles from './index.module.scss';
import moment from 'moment';
import { BillDetailStatusMap } from '../../constants';

const getPrice = (price) => {
  if (!price) return '--';
  return price;
}


export const getColumns = current => {

  return [
    {
      title: '用户账号',
      dataIndex: 'extInfo',
      lock: 'left',
      width: 120,
      cell: val => <>【{val?.tpBuyerId}】{val?.tpBuyerNick}</>
    },
    {
      title: '明细ID',
      dataIndex: 'id',
      lock: 'left',
      width: 100
    },
    {
      title: '结算单号',
      dataIndex: 'settleOrderNo',
      lock: 'left',
      width: 100
    },
    {
      title: '采购父订单号',
      dataIndex: 'parentTpOrderId',
      width: 120
    },
    {
      title: '采购子订单号',
      dataIndex: 'tpOrderId',
      width: 120
    },
    {
      title: '销售主单号',
      dataIndex: 'parentBizOrderNo',
      width: 120
    },
    {
      title: '销售子单号',
      dataIndex: 'bizOrderNo',
      width: 120
    },
    {
      title: '外部订单号',
      dataIndex: 'outOrderId',
      width: 120
    },
    {
      title: '下单时间',
      dataIndex: 'gmtCreate',
      width: 120,
      align: 'left',
      cell: (val) => moment(val).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '物流确收时间',
      dataIndex: 'logisticsConfirmTime',
      width: 120,
      cell: (val) => moment(val).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '订单状态',
      dataIndex: 'status',
      width: 120,
      cell: val => <>{BillDetailStatusMap[val] || '--'}</>
    },
    {
      title: '下单发票纳税人识别号',
      dataIndex: 'extInfo',
      width: 300,
      cell: val => <>{val?.taxCode || '--'}</>
    },
    {
      title: '商品编号',
      dataIndex: 'egoItemId',
      width: 200,
      cell: val => <>{val || '--'}</>
    },
    {
      title: '商品名称',
      dataIndex: 'extInfo',
      width: 200,
      cell: val => <>{val?.auctionName || '--'}</>
    },
    {
      title: '商品单价',
      dataIndex: 'itemPrice',
      width: 150,
      align: 'left',
      cell: (val, _idx, record)  => {
        const singleVal = Number(val);
        return <>
          {/* <div className={styles.tableDataItem}>
            <span className={styles.tableItemLabel}>¥{' '}{getPrice(singleVal)}</span>
            <span>不含税</span>
          </div> */}
          <div className={styles.tableDataItem}>
            <span className={styles.tableItemLabel}>¥{' '}{getPrice(singleVal)}</span>
            <span>含税</span>
          </div>
        </>
      }
    },
    {
      title: '商品总额',
      width: 150,
      dataIndex: 'amount',
      cell: (val, _idx, record)  => {
        return <>
          {/* <div className={styles.tableDataItem}>
            <span className={styles.tableItemLabel}>¥{' '}{getPrice(val)}</span>
            <span>不含税</span>
          </div> */}
          <div className={styles.tableDataItem}>
            <span className={styles.tableItemLabel}>¥{' '}{getPrice(val)}</span>
            <span>含税</span>
          </div>
        </>
      }
    },
    {
      title: '商品数量',
      dataIndex: 'extInfo',
      width: 80,
      align: 'right',
      cell: val => <>{val?.buyAmount}</>
    },
    {
      title: '商品类型',
      dataIndex: 'XX',
      width: 80,
      align: 'right',
      cell: val => <>{val?.buyAmount}</>
    },
    {
      title: '商品税率',
      dataIndex: 'extInfo',
      width: 100,
      cell: val => <>{val?.taxRate || '--'}</>
    },
    {
      title: '商品型号',
      dataIndex: 'extInfo',
      width: 100,
      cell: val => <>{val?.xxxx || '--'}</>
    },
    {
      title: '销售单位ID',
      dataIndex: 'extInfo',
      width: 160,
      cell: (val) => <>{val?.tpSellerId}</>
    },
    {
      title: '出账时间',
      width: 120,
      align: 'right',
      cell: () => moment(current?.extInfo?.billGenDate).format('YYYY-MM-DD')
    },
    {
      title: '应还款日',
      width: 120,
      align: 'right',
      cell: () => moment(current?.settleEndDate).format('YYYY-MM-DD')
    },
    {
      title: '收货人',
      dataIndex: 'extInfo',
      width: 240,
      align: 'left',
      cell: (val) => {
        return <>
          <div className={styles.tableDataItem}>
            <span className={styles.tableItemLabel}>收货人</span>
            <span>{' '}{val?.tpBuyerNick}</span>
          </div>
          <div className={styles.tableDataItem}>
            <span className={styles.tableItemLabel}>收货地址/区域</span>
            <span>{' '}{val?.tpBuyerNick}</span>
          </div>
          <div className={styles.tableDataItem}>
            <span className={styles.tableItemLabel}>收货电话</span>
            <span>{' '}{val?.tpBuyerId}</span>
          </div>
        </>
      }
    },
    // {
    //       title: '发票',
    //       dataIndex: 'ticket',
    //       width: 240,
    //       lock: 'right',
    //       align: 'left',
    //       cell: (val) => {
    //         return <>
    //           <div className={styles.tableDataItem}>
    //             <span className={styles.tableItemLabel}>开票状态</span>
    //             <span>{val?.status}</span>
    //           </div>
    //           <div className={styles.tableDataItem}>
    //             <span className={styles.tableItemLabel}>开票类型</span>
    //             <span>{val?.type}</span>
    //           </div>
    //           <div className={styles.tableDataItem}>
    //             <span className={styles.tableItemLabel}>开票时间</span>
    //             <span>{val?.data}</span>
    //           </div>
    //           <div className={styles.tableDataItem}>
    //             <span className={styles.tableItemLabel}>发票抬头</span>
    //             <span>{val?.data}</span>
    //           </div>
    //           <div className={styles.tableDataItem}>
    //             <span className={styles.tableItemLabel}>发票金额</span>
    //             <span>{val?.data}</span>
    //           </div>
    //           <div className={styles.tableDataItem}>
    //             <span className={styles.tableItemLabel}>发票号码</span>
    //             <span>{val?.data}</span>
    //           </div>
    //         </>
    //       }
    //     },
  ]
}