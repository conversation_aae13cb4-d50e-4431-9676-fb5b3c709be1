
.billDetailDrawer {
  :global {
    .next-drawer-body {
      padding: 24px;
    }
  }

  :global(.next-pagination-display) {
    min-width: 25px;
    margin: 0;
  }
}

.basicInfo {
  margin-bottom: 24px;

  h3 {
    color: #111;
    font-weight: 600;
    font-size: 14px;
    margin: 0 0 16px 0;
  }
}

.billAmount {
  display: flex;
  align-items: center;
  h3 {
    margin: 0 18px 0 0;
  }
}

.infoGrid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  border: 1px solid #e4e6ed;
  padding: 12px;
  border-radius: 12px;
}

.infoItem {
  display: flex;
  gap: 8px;
  font-size: 12px;
  color: #111;
  max-width: 280px;

  .label {
    color: #999;
    min-width: 100px;
  }
  .labelContent {
    flex: 1;
    overflow: hidden;
    word-wrap: break-word;
  }
}

.amountInfo {
  display: flex;
  gap: 32px;
}

.amountItem {
  display: flex;
  align-items: baseline;
  gap: 4px;
  border: 1px solid #e4e6ed;
  border-radius: 12px;
  padding: 12px;
  color: #111;

  .label {
    font-size: 12px;
  }
}
.popupContainer {
  width: 280px;
  height: 300px;

  .moveList {
    height: 180px;
    overflow-y: scroll;
    margin-top: 8px;
  }
  .searchInput {
    width: 100%;
  }
  .btnBox {
    margin-top: 8px;
    display: flex;
    justify-content: flex-end;
  }
  .activeListItem {
    background: rgba(61, 94, 255, 0.06);
  }
}
.orderList {
  .orderHeader {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
  }

  .detailHeader {
    display: flex;
    align-items: center;
    height: 50px;
    justify-content: space-between;
    font-size: 12px;
    .mainOp {
      display: flex;
      align-items: center;
      color: #3d5eff;
      .hasChoose {
        color: #111;
        cursor: auto;
        width: 36px;
      }
      div {
        margin-right: 10px;
        cursor: pointer;
      }
    }
    .subOp {
      display: flex;
      align-items: center;
      .selectRowOpContainer {
        position: relative;
        .selectRowOp {
          color: #666;
          margin-right: 12px;
          cursor: pointer;
        }
      }

      .total {
        color: #999999;
        margin-right: 12px;
      }

      .page {
        :global(.next-btn:disabled) {
          background-color: #fff;
        }
        :global(.next-pagination-item) {
          border: none;
        }
      }
    }
  }
  .searchSection {
    display: flex;
    gap: 8px;
  }
}


.priceContainer {
  display: flex;
  align-items: baseline;
  font-weight: 500;
  .currency {
    font-size: 14px;
  }
  .integer {
    font-size: 20px;
  }
  .decimal {
    font-size: 16px;
  }
}

.tableDataItem {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #999;
  .tableItemLabel {
    color: #111;
    margin-right: 4px;
  }
}

.selectBox {
  .checkboxItem {
    display: inline-block;
    margin-bottom: 10px;
  }

  .selectBtnBox {
    display: flex;
    justify-content: flex-end;
  }
}