.confirmModal {
  width: 600px;

  :global {
    .next-dialog-body {
      padding: 24px;
    }
  }
}

.section {
  margin-bottom: 24px;

  h3 {
    font-size: 14px;
    font-weight: 600;
    color: #111;
    margin: 0 0 16px 0;
  }
}

.infoGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.infoItem {
  display: flex;
  gap: 8px;
  color: #111;
  span {
    word-break: break-all;
  }
  .label {
    color: #999;
    min-width: 120px;
  }
}

.completeDate {
  color: #333;
}

.footer {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;

  button {
    padding: 8px 24px;
    border-radius: 4px;
    border: none;
    cursor: pointer;
    font-size: 14px;
  }
}