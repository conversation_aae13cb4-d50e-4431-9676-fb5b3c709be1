import React, { useEffect, useMemo, useState } from 'react';
import { Dialog, Icon, Message } from '@alifd/next';
import clsx from 'clsx';

import styles from './index.module.scss';
import s from '../../styles/common.module.scss';
import { getCorporateBillItem, updateBillStatus } from '@/services';
import moment from 'moment';

interface ConfirmModalProps {
  visible: boolean;
  setVisible: (args: boolean) => void;
  current: Record<string, any>;
  getList: () => void;
}

const ConfirmModal: React.FC<ConfirmModalProps> = ({
  visible,
  setVisible,
  current,
  getList,
}) => {
  const [invoiceInfo, setInvoiceInfo] = useState<Record<string, string>>({});

  const updateParams = useMemo(() => {
    if (!current) return {};
    const buyerId = JSON.parse(current?.contractSnap || '{}')?.buyerId || '';
    return {
      buyerId: buyerId,
      id: current?.id,
      status: 'lock',
    }
  }, [current])
  const onConfirm = async () => {
    if (Object.keys(updateParams).length === 0) {
      Message.error('账单相关信息为空，代客确认失败，请稍后重试');
      return;
    }
    // 点击确认
    const result = await updateBillStatus(updateParams)
    setVisible(false);
    console.log('======result', result)
    if (result.success) {
      Message.success('代客确认成功');
      onClose?.();
      getList();
    } else {
      Message.error(result?.errorMsg || '代客确认失败，请稍后重试');
    }
  };

  const onClose = () => {
    // 点击取消
    setVisible(false);
  }

  useEffect(() => {
    if (!visible) return;
    getCorporateBillItem({
      batchSettleOrderNo: current?.batchSettleOrderNo,
    }).then(res => {
      setInvoiceInfo(res?.data?.invoiceInfoDTO || {});
    })
  }, [visible])


  if (!current) return null;


  return (
    <Dialog
      visible={visible}
      onClose={onClose}
      title="代客确认"
      closeMode={["close", "mask"]}
      footer={false}
      className={styles.confirmModal}
    >
      <div className={s.warningInfo}>
        <div className={s.warningIcon}><Icon type="prompt" /></div>
        <div>一经提交不可修改！将进入平台开票流程，请务必与客户确认</div>
      </div>
      {/* <div className={clsx({
        [s.warningInfo]: true,
        [s.errorInfo]: true,
      })}>
        <div className={s.warningIcon}><Icon type="error" /></div>
        <div>账单内有订单已经汇总在其他账单中，账单号：987654678911123，987654678911123，987654678911123</div>
      </div> */}
      <div className={styles.section}>
        <h3>基本信息</h3>
        <div className={styles.infoGrid}>
          <div className={styles.infoItem}>
            <span className={styles.label}>企业名称</span>
            <span>{(window as any)?._curBuyerInfo}</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>合同ID</span>
            <span>{current?.contractId}</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>账单ID</span>
            <span>{current?.batchSettleOrderNo}</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>账单名称</span>
            <span>{current?.extDTO?.batchSettleOrderName}</span>
          </div>
        </div>
      </div>

      <div className={styles.section}>
        <h3>发票信息</h3>
        <div className={styles.infoGrid}>
          <div className={styles.infoItem}>
            <span className={styles.label}>购方名称</span>
            <span>{invoiceInfo?.partnerName}</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>购方税号</span>
            <span>{invoiceInfo?.partnerTaxNo}</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>购方地址</span>
            <span>{invoiceInfo?.invoiceAddress}</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>购方开户行</span>
            <span>{invoiceInfo?.partnerBank}</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>购方类型</span>
            <span>{current?.bizType}</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>购方电话</span>
            <span>{invoiceInfo?.invoiceTelephoneNumber}</span>
          </div>
          <div className={styles.infoItem}>
            <span className={styles.label}>购方开户银行账号</span>
            <span>{current?.partnerNo}</span>
          </div>
        </div>
      </div>

      <div className={styles.section}>
        <h3>完款日</h3>
        <div className={styles.infoItem}>
          <span className={styles.label}>完款日</span>
          <span> {current?.billEndDate ? moment(current?.billEndDate).format('YYYY-MM-DD') : ''}</span>
        </div>
      </div>

      <div className={styles.footer}>
        <button className={clsx(s.btnPrimary, s.btnHighlight)} onClick={onClose}>取消</button>
        <button className={clsx(s.btnPrimary, s.btnActive)} onClick={onConfirm}>确认</button>
      </div>
    </Dialog>
  );
};

export default ConfirmModal;