import JSZip from 'jszip';
const isEmptyArray = (arr) => {
  return Array.isArray(arr) && arr.length === 0;
}

/**
 * 移除参数的空值
 */
export const removeUndefinedKeys = (obj) => {
  return Object.keys(obj).reduce((acc, key) => {
    if (obj[key] !== undefined && obj[key] !== '' && !isEmptyArray(obj[key])) {
        acc[key] = obj[key];
    }
    return acc;
  }, {});
}

export const convertFenToYuan = (fen) => {
  // 如果fen是空字符串或undefined，返回"0.00元"
  if (fen === '' || fen === undefined) {
    return '0.00元';
  }
  // 将fen转化为整数
  const fenValue = parseInt(fen, 10);

  // 如果fenValue不是有效数字，返回"0.00元"
  if (isNaN(fenValue)) {
    return '0.00元';
  }

  // 将分转换为元并格式化为两位小数
  const yuanValue = (fenValue / 100).toFixed(2);

  // 返回格式化后的字符串
  return `${yuanValue}元`;
}

export const convertBase64ListToPdf = (base64StringList, batchSettleOrderNo) => {
  const zip = new JSZip();
  // 将 Base64 字符串转换为文件类型数据
  for (let i = 0; i < base64StringList.length; i++) {
    const base64Data = base64StringList[i];

    const binaryString = window.atob(base64Data.eInvoiceData);
    const len = binaryString.length;
    const bytes = new Uint8Array(len);

    for (let j = 0; j < len; j++) {
      bytes[j] = binaryString.charCodeAt(j);
    }

    // 添加 PDF 到 zip 中
    zip.file(`${base64Data.invoiceNo || Date.now()}_${i}.pdf`, bytes);
  }
  // 生成 ZIP 文件并触发下载
  zip.generateAsync({ type: 'blob' }).then(function(content) {
    const link = document.createElement("a");
    link.href = URL.createObjectURL(content);
    link.download = `${batchSettleOrderNo}.zip`;
    link.click();
  });
}