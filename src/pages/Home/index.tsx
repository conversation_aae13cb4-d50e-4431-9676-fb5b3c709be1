import React, { useEffect } from 'react';
import { Link } from 'ice';
import styles from './index.module.scss';

export default function Home() {
  useEffect(() => {
    console.log('Home Page mounted');
    return () => {
      console.log('Home Page unmounted');
    };
  }, []);

  return (
    <div className={styles.container}>
      <h2>Home Page</h2>
      <Link to="/list">列表页面</Link>
      <div className={styles.aaa}>测试</div>
    </div>
  );
}
