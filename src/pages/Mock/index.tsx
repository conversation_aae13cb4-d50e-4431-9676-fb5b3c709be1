const urlParams = new URLSearchParams(window.location.search);
const poolId = Number(urlParams.get('poolId')) || 500001;

export const productPoolListMockData = [
  {
    title: '(测试商品请不要拍)原品=************',
    itemId: ************,
    itemDetailUrl: 'https://detail.tmall.com/item.htm?id=************',
    imageUrl: 'https://img.alicdn.com/imgextra/i1/88591187/O1CN01Q8plTp1Kdh7blwd88_!!0-item_pic.jpg_160x160xz_.webp',
    brandName: '美的',
    leafCategoryName: '工业风扇',
    sellerNick: '品恒',
    taxCode: '*************567891',
    taxRate: '13%',
    egoTaxCode: '*************567891',
    egoTaxRate: '16%',
    priceRange: '102.00～120.00',
    referencePriceRange: '99.00～105.00',
    egoPriceRange: '88.00～90.00',
    quantity: 100,
    gmtCreate: '2024-02-14 07:00:00',
    applyStatus: 'waiting',
    auditStatus: 'success',
    operateList: ['audit'],
  },
  {
    title: '(测试商品请不要拍)原品=************',
    itemId: ************,
    itemDetailUrl: 'https://detail.tmall.com/item.htm?id=************',
    imageUrl: 'https://img.alicdn.com/imgextra/i1/88591187/O1CN01Q8plTp1Kdh7blwd88_!!0-item_pic.jpg_160x160xz_.webp',
    brandName: '海尔',
    leafCategoryName: '家用电器',
    sellerNick: '家电专卖',
    taxCode: '2234567891234567891',
    taxRate: '15%',
    egoTaxCode: '2234567891234567891',
    egoTaxRate: '15%',
    priceRange: '200.00～250.00',
    referencePriceRange: '190.00～210.00',
    egoPriceRange: '180.00～200.00',
    quantity: 50,
    gmtCreate: '2024-02-15 08:00:00',
    applyStatus: 'waiting',
    auditStatus: 'fail',
    operateList: ['sell'],
  },
  {
    title: '(测试商品请不要拍)原品=************',
    itemId: ************,
    itemDetailUrl: 'https://detail.tmall.com/item.htm?id=************',
    imageUrl: 'https://img.alicdn.com/imgextra/i1/88591187/O1CN01Q8plTp1Kdh7blwd88_!!0-item_pic.jpg_160x160xz_.webp',
    brandName: '格力',
    leafCategoryName: '空调',
    sellerNick: '空调专卖',
    taxCode: '3234567891234567891',
    taxRate: '10%',
    egoTaxCode: '3234567891234567891',
    egoTaxRate: '10%',
    priceRange: '300.00～350.00',
    referencePriceRange: '290.00～310.00',
    egoPriceRange: '280.00～300.00',
    quantity: 75,
    gmtCreate: '2024-02-16 09:00:00',
    applyStatus: 'waiting',
    auditStatus: 'auditing',
    operateList: ['return'],
  },
];

export const productPoolCountMockData = {
  countList: [
    {
      type: 'all',
      count: 36,
    },
    {
      type: 'auditing',
      count: 12,
    },
    {
      type: 'success',
      count: 10,
    },
    {
      type: 'fail',
      count: 2,
    },
  ],
};

export const productPoolBaseInfoMockData = {
  poolId: poolId,
  poolRule: {
    discountRateReference: 0.9,
    discountRateOrigin: 0.9,
    shipTime: 48,
    taxRateDiffType: 'equal',
  },
  project: {
    projectId: 1234,
    projectName: '工商银行积分兑换',
    enterpriseName: '工商银行',
  },
  countList: [
    {
      type: 'add',
      count: 0,
    },
    {
      type: 'waiting',
      count: 9999,
    },
  ],
};

export const itemsListMockData = [
  {
    title: '(测试商品请不要拍)原品=************',
    itemId: ************,
    itemDetailUrl: 'https://detail.tmall.com/item.htm?id=************',
    imageUrl: 'https://img.alicdn.com/imgextra/i1/88591187/O1CN01Q8plTp1Kdh7blwd88_!!0-item_pic.jpg_160x160xz_.webp',
    brandName: '美的',
    leafCategoryName: '工业风扇',
    sellerNick: '品恒',
    taxCode: '*************567891',
    taxRate: '13%',
    egoTaxCode: '*************567891',
    egoTaxRate: '13%',
    priceRange: '102.00～120.00',
    referencePriceRange: '99.00～105.00',
    egoPriceRange: '88.00～90.00',
    quantity: 100,
    gmtCreate: '2024-02-14 07:00:00',
    egoItemStatus: 'normal',
    operateList: ['offline'],
  },
];

export const skuDetailMockData = [
  {
    skuId: '5895428906002',
    skuImageUrl: 'https://img.alicdn.com/imgextra/i1/88591187/O1CN01Q8plTp1Kdh7blwd88_!!0-item_pic.jpg_160x160xz_.webp',
    prop: '卡路里拜拜片-急控款',
    skuPrice: '104.00',
    referencePrice: '99.00',
    egoPrice: '88.00',
  },
];

export const operationRecordMockData = [
  {
    operateTime: '2025-02-01 00:00',
    operator: '恒夏/226940',
    operation: '新增商品',
    details: '************',
  },
  {
    operateTime: '2025-02-02 10:30',
    operator: '李明/123456',
    operation: '修改价格',
    details: '商品ID: ************, 新价格: 110.00',
  },
  {
    operateTime: '2025-02-03 14:45',
    operator: '王强/789012',
    operation: '删除商品',
    details: '商品ID: ************',
  },
  {
    operateTime: '2025-02-04 09:15',
    operator: '张伟/345678',
    operation: '更新库存',
    details: '商品ID: ************, 新库存: 150',
  },
  {
    operateTime: '2025-02-05 16:00',
    operator: '赵丽/901234',
    operation: '审核通过',
    details: '商品ID: 884362056515',
  },
];

export const projectListMockData = [
  {
    projectId: poolId,
    projectName: '企业合同xxx',
    enterprise: {
      enterpriseId: 1234,
      shortName: '企业xxx',
    },
    itemPool: {
      itemTotalNum: 500,
    },
    contract: {
      contractId: 1234,
      contractAmount: 50000,
    },
    gmtCreate: '2025-01-23 12:00:00',
    gmtModified: '2025-01-23 12:00:00',
    updateOperator: '226940/恒夏',
    operateList: ['edit', 'audit'],
    effectiveStatus: 'valid',
    status: 'edit',
  },
];

export const enterpriseListMockData = [
  {
    enterpriseId: 1234,
    shortName: '品恒',
    projectList: [
      {
        projectId: 1234,
        projectName: '企业合同xxx',
      },
    ],
    gmtCreate: '2025-01-23 12:00:00',
    gmtModified: '2025-01-23 12:00:00',
    updateOperator: '226940/恒夏',
    operateList: ['edit', 'audit'],
    effectiveStatus: 'valid',
    status: 'edit',
  },
  {
    enterpriseId: 1235,
    shortName: '企业yyy',
    projectList: [
      {
        projectId: 1235,
        projectName: '企业合同yyy',
      },
    ],
    gmtCreate: '2025-02-01 10:00:00',
    gmtModified: '2025-02-01 10:00:00',
    updateOperator: '123456/李明',
    operateList: ['edit', 'audit'],
    effectiveStatus: 'valid',
    status: 'edit',
  },
  {
    enterpriseId: 1236,
    shortName: '企业zzz',
    projectList: [
      {
        projectId: 1236,
        projectName: '企业合同zzz',
      },
    ],
    gmtCreate: '2025-03-01 11:00:00',
    gmtModified: '2025-03-01 11:00:00',
    updateOperator: '789012/王强',
    operateList: ['edit', 'audit'],
    effectiveStatus: 'valid',
    status: 'edit',
  },
  {
    enterpriseId: 1237,
    shortName: '企业aaa',
    projectList: [
      {
        projectId: 1237,
        projectName: '企业合同aaa',
      },
    ],
    gmtCreate: '2025-04-01 09:00:00',
    gmtModified: '2025-04-01 09:00:00',
    updateOperator: '345678/张伟',
    operateList: ['edit', 'audit'],
    effectiveStatus: 'valid',
    status: 'edit',
  },
  {
    enterpriseId: 1238,
    shortName: '企业bbb',
    projectList: [
      {
        projectId: 1238,
        projectName: '企业合同bbb',
      },
    ],
    gmtCreate: '2025-05-01 08:00:00',
    gmtModified: '2025-05-01 08:00:00',
    updateOperator: '901234/赵丽',
    operateList: ['edit', 'audit'],
    effectiveStatus: 'valid',
    status: 'edit',
  },
  {
    enterpriseId: 1239,
    shortName: '企业ccc',
    projectList: [
      {
        projectId: 1239,
        projectName: '企业合同ccc',
      },
    ],
    gmtCreate: '2025-06-01 07:00:00',
    gmtModified: '2025-06-01 07:00:00',
    updateOperator: '567890/刘强',
    operateList: ['edit', 'audit'],
    effectiveStatus: 'valid',
    status: 'edit',
  },
];

export const enterpriseDetailMockData = {
  enterpriseId: 1234,
  shortName: '企业xxx',
  fullName: '企业xxx',
  creditIdentifier: '91330108MA2AYTH7XU',
  businessLicenseUrl: 'https://zzlz.gsxt.gov.cn/businessCheck/viewPdfImage?licence13=913301107517434382',
  subList: [
    {
      enterpriseId: 1234,
      shortName: '企业xxx',
      fullName: '企业xxx',
      creditIdentifier: '91330108MA2AYTH7XU',
      createTime: '2025-01-23 12:00:00',
    },
  ],
  status: 'edit',
};

export const subProjectDetailMockData = {
  projectId: 1234,
  projectName: '企业合同xxx',
  enterprise: {
    enterpriseId: 1234,
    shortName: '企业xxx',
  },
  contract: {
    contractId: 1234,
    contractName: '企业合同xxx',
    startDate: '2025-01-23',
    endDate: '2025-01-24',
    contractAmount: 50000,
  },
  invoice: {
    idenNumber: '*************',
    title: '品恒',
    contactName: '韩梅梅',
    contactPhone: '***********',
    address: '杭州市xxx',
    bankName: '浙江分行',
    bankAccount: '6227000000000000000',
  },
  settle: {
    rebateRate: '3.0',
    orderInterval: 7,
    settleIntervalType: 'month',
    settleInterval: 7,
  },
  gmtCreate: '2025-01-23 12:00:00',
  gmtModified: '2025-01-23 12:00:00',
  updateOperator: '226940/恒夏',
  status: 'edit',
};

export const merchantListMockData = [
  {
    sellerId: 1234,
    taoSellerId: 1234,
    shortName: '品恒',
    shopName: '品恒xxx',
    contract: {
      contractId: 1234,
    },
    gmtCreate: '2025-01-23 12:00:00',
    gmtModified: '2025-01-23 12:00:00',
    updateOperator: '226940/恒夏',
    operateList: ['edit', 'audit'],
    effectiveStatus: 'valid',
    status: 'edit',
  },
  {
    sellerId: 12345,
    taoSellerId: 12345,
    shortName: '品恒',
    shopName: '品恒xxx',
    contract: {
      contractId: 1234,
    },
    gmtCreate: '2025-01-23 12:00:00',
    gmtModified: '2025-01-23 12:00:00',
    updateOperator: '226940/恒夏',
    operateList: ['edit', 'audit'],
    effectiveStatus: 'valid',
    status: 'edit',
  },
];

export const merchantDetailMockData = {
  sellerId: 1234,
  taoSellerId: 1234,
  shortName: '品恒',
  shopName: '品恒xxx',
  contract: {
    contractId: 1234,
    contractName: '企业合同xxx',
    startDate: '2025-01-23',
    endDate: '2025-01-24',
  },
  invoice: {
    idenNumber: '*************',
    title: '品恒',
    contactName: '韩梅梅',
    contactPhone: '***********',
    address: '杭州市xxx',
    bankName: '浙江分行',
    bankAccount: '6227000000000000000',
  },
  settle: {
    rebateRate: '3.0',
    billCycle: 1,
    billCycleType: 'day',
    settleCycle: 5,
    settleCycleType: 'day',
  },
  status: 'edit',
};

export const servicerListMockData = [
  {
    servicerId: 1234,
    shortName: '品恒',
    contract: {
      contractId: 1234,
    },
    settle: {
      saleServiceRate: '3.0',
    },
    gmtCreate: '2025-01-23 12:00:00',
    gmtModified: '2025-01-23 12:00:00',
    updateOperator: '226940/恒夏',
    operateList: ['edit', 'audit'],
    effectiveStatus: 'valid',
    status: 'edit',
  },
];

export const servicerDetailMockData = {
  servicerId: 1234,
  shortName: '品恒',
  fullName: '品恒',
  contract: {
    contractId: 1234,
    contractName: '企业合同xxx',
    startDate: '2025-01-23',
    endDate: '2025-01-24',
  },
  settle: {
    saleServiceRate: '3.0',
    billCycle: 1,
    billCycleType: 'day',
    settleCycle: 5,
    settleCycleType: 'day',
  },
  status: 'edit',
};
