
.orderManagement {
  padding: 20px;
  background: #f5f5f5;

  :global {
    .next-card {
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }
  }
}

.infoSection {
  display: flex;
  gap: 40px;
  padding-bottom: 24px;
  margin-bottom: 24px;
  border-bottom: 1px solid #e8e8e8;
}

.infoColumn {
  flex: 1;

  h3 {
    font-size: 16px;
    font-weight: 500;
    color: #333;
    margin-bottom: 16px;
  }
}

.infoItem {
  display: flex;
  margin-bottom: 12px;
  font-size: 14px;
  line-height: 1.5;

  :global(.next-typography) {
    &:first-child {
      color: #666;
    }

    &:last-child {
      color: #333;
      flex: 1;
    }
  }
}

.productCell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.productImage {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
}

.productInfo {
  flex: 1;
}

.productTitle {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
  line-height: 1.4;
  display: flex;
  .linkBtn {
    margin-left: 8px;
  }
}
.productSku {
  font-size: 12px;
  color: #999;
}

.orderTable {
  :global {
    .next-table-header {
      background: #fafafa;
    }

    .next-table-cell {
      color: #333;
    }
  }
}