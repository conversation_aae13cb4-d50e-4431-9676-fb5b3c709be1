import React, { useCallback, useEffect, useState } from 'react';
import { Table, Card, Typography, Message, Button, Icon } from '@alifd/next';

import { queryEnterpriseOrderDetail } from '@/services';
import { queryEnterpriseOrderDetailRes } from '@/types';

import styles from './index.module.scss';

const { Text } = Typography;

// 商品表格列配置
const productColumns = [
  {
    title: '商品',
    dataIndex: 'product',
    cell: (value, index, record) => (
      <div className={styles.productCell}>
        <img src={record.itemPic} alt="商品图片" className={styles.productImage} />
        <div className={styles.productInfo}>
          <div className={styles.productTitle}>
            <div>商品ID: {record.itemId}</div>
            <Button className={styles.linkBtn} text type='primary' onClick={() => { window.open(`https://item.taobao.com/item.htm?id=${record.itemId}`) }}>商品详情<Icon type="arrow-right" /></Button>
          </div>
          <div className={styles.productTitle}>商品名: {record.itemName}</div>
          <div className={styles.productSku}>SKUID: {record.skuId}</div>
          <div className={styles.productSku}>SKU名: {record.skuProperty}</div>
        </div>
      </div>
    )
  },
  {
    title: '价格',
    dataIndex: 'itemPrice',
    width: 100,
    cell: (val) => processString(val)
  },
  {
    title: '购买数量',
    dataIndex: 'buyAmount',
    width: 100,
  },
  {
    title: '物流状态',
    dataIndex: 'logistics',
    width: 100,
    cell: value => {
      if (!value) {
        return '暂无';
      }
      return <>
        {
          value?.map((v, i) => {
            return <div key={i}>{v.standardDesc || '暂无'}</div>
          })
        }
      </>
    }
  },
  {
    title: '淘系订单ID',
    dataIndex: 'tpOrderId',
    width: 200,
    cell: value => {
      if (!value) {
        return '--'
      }
      return <Button className={styles.linkBtn} text type='primary' onClick={() => { window.open(`https://trade.cacheadmin.taobao.org/tccacheadmin/niceTpConsole.htm?op=niceQuerySingle&queryType=main&id==${value}`) }}>{value}</Button>
    }
  }
];

const processString = (input) =>{
  // 尝试将输入解析为浮点数
  const number = parseFloat(input);
  if (isNaN(number)) {
    return '--';
  } else {
    return `${number / 100}元`;
  }
}
const InfoItem = ({ label, value }: { label: string; value: string | number | null | undefined }) => (
  <div className={styles.infoItem}>
    <Text>{label}：</Text>
    <Text>{value ?? '--'}</Text>
  </div>
);

const OrderDetail = () => {

  const [orderDetail, setOrderDetail] = useState<queryEnterpriseOrderDetailRes>({} as queryEnterpriseOrderDetailRes);

  const queryParams = new URLSearchParams(location.search);
  const orderId = queryParams.get('orderId');
  const buyerId = queryParams.get('buyerId');
  const buyerName = queryParams.get('buyerName') || '未知用户';

  const fetchOrderDetail = useCallback(async () => {
    if (!orderId || !buyerId) {
      Message.notice('请传入orderId和buyerId');
      return;
    }

    const data = await queryEnterpriseOrderDetail({
      bizOrderId: orderId,
      buyerId: buyerId,
    });

    if (!data?.success || !data?.data) {
      Message.error(data?.errorMsg || '获取失败，请稍后重试');
      return;
    }

    setOrderDetail(data?.data as queryEnterpriseOrderDetailRes);

  }, [orderId, buyerId])

  useEffect(() => {
    fetchOrderDetail();
  }, [])

  return  <div className={styles.orderManagement}>
    <Card contentHeight="auto">
      <div className={styles.infoSection}>
        <div className={styles.infoColumn}>
          <h3>交易信息</h3>
          <InfoItem label="订单ID" value={orderDetail?.bizOrderId} />
          <InfoItem label="外部订单ID" value={orderDetail?.outOrderId} />
          <InfoItem label="订单创建时间" value={orderDetail?.createTime} />
          <InfoItem label="订单金额" value={processString(orderDetail?.payInfo?.totalPrice)} />
          <InfoItem label="服务商ID" value={orderDetail?.outServiceId} />
          <InfoItem label="服务商名" value={orderDetail?.outServiceNick} />
        </div>

        <div className={styles.infoColumn}>
          <h3>买家信息</h3>
          <InfoItem label="企业ID" value={orderDetail?.buyerId} />
          <InfoItem label="社会信用代码" value={orderDetail?.socialCreditCode} />
          <InfoItem label="项目ID" value={orderDetail?.contractNo} />
          <InfoItem label="企业用户账号ID&用户名" value={buyerName} />
        </div>

        <div className={styles.infoColumn}>
          <h3>地址信息</h3>
          <InfoItem label="收货人" value={orderDetail?.receiver?.receiverName} />
          <InfoItem label="联系方式" value={orderDetail?.receiver?.mobile} />
          <InfoItem label="收货地址" value={orderDetail?.receiver?.addressDetail} />
        </div>
      </div>

      <Table
        dataSource={orderDetail?.subOrderDetails || []}
        columns={productColumns}
        hasBorder={false}
        className={styles.orderTable}
      />
    </Card>
  </div>
};

export default OrderDetail;