import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Button, Tab, Table, Pagination, Tag, Message, Icon,  Balloon } from '@alifd/next';
import SkuDetail from '@/components/ItemPoolComp/skuDetail';
import SearchForm from '@/components/ItemPoolComp/searchForm';
import AuditDialog from '@/components/ItemPoolComp/auditDialog';

import { queryItemsPoolList, queryItemsPoolListCount, queryItemsPoolSkuList, auditItem } from '@/services';
import { auditStatusOptions, applyStatusOptions, isTrue, formatTimestamp } from '../Utils';
import { CountItem, Product } from '@/types';

import styles from './index.module.scss';

const ItemPoolManage = () => {
  const [isAuditDialogVisible, setIsAuditDialogVisible] = useState(false);
  const [isSkuDetailVisible, setIsSkuDetailVisible] = useState(false);
  const [searchParams, setSearchParams] = useState({ auditStatus: 'all' });
  const [pagination, setPagination] = useState({ current: 1, pageSize: 20, total: 0 });
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState([]);
  const [countList, setCountList] = useState<CountItem[]>([]);
  const searchFormRef = useRef<{ getValues: () => Record<string, any> } | null>(null);
  const [currentItemData, setCurrentItemData] = useState<any>(null);
  const [selectedRows, setSelectedRows] = useState<Product[]>([]);
  const isPendingAudit = searchParams.auditStatus === 'auditing';

  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const formData = searchFormRef?.current?.getValues() || {};
      const params = { ...searchParams, ...formData, currentPage: pagination.current, pageSize: pagination.pageSize };
      const { auditStatus, ...restParams } = params;
      const queryParams = auditStatus === 'all' ? restParams : params;
      const [listResponse, countResponse] = await Promise.all([
        queryItemsPoolList({
          ...queryParams,
        }),
        queryItemsPoolListCount(restParams),
      ]);
      setDataSource(listResponse?.data?.dataList || []);
      setPagination((prev) => ({ ...prev, total: listResponse.data?.totalCount || 0 }));
      setCountList(countResponse?.data?.countList || []);
    } catch (error) {
      console.error('数据获取失败:', error);
      setLoading(false);
    } finally {
      setLoading(false);
    }
  }, [searchParams, pagination.current, pagination.pageSize]);

  const handleRefresh = useCallback(() => {
    setSearchParams({ auditStatus: 'all' });
    setPagination((prev) => ({ ...prev, current: 1, pageSize: 20 }));
    searchFormRef.current?.getValues && searchFormRef.current.reset?.();
  }, []);

  const handleSearch = useCallback(() => {
    setPagination((prev) => ({ ...prev, current: 1 })); // Reset current page to 1
    fetchData();
  }, [fetchData]);

  const handleBatchAudit = () => {
    if(selectedRows.length === 0) {
      Message.error('请先选择商品');
      return;
    }
    setIsAuditDialogVisible(true);
  };

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const renderActions = (value, index, record) => {
    return (
      <div className={styles.actions}>
        <Button
          type="primary"
          text
          onClick={() => {
            queryItemsPoolSkuList({ itemId: record.itemId }).then((res) => {
              if (isTrue(res?.success))
                setCurrentItemData({
                  ...record,
                  skuList: res?.data || [],
                });
              setIsSkuDetailVisible(true);
            });
          }}
          style={{ marginRight: '5px' }}
        >
          查看sku信息
        </Button>

        {record?.operateList?.includes('audit') && (
          <Button
            type="primary"
            text
            onClick={() => {
              setCurrentItemData(record);
              setIsAuditDialogVisible(true);
            }}
          >
            审核
          </Button>
        )}
      </div>
    );
  };

  const renderImageInfo = (value) => {
    const { imageUrl, title, itemId, itemDetailUrl } = value;
    return (
      <div className={styles.imageInfoContainer}>
        <div className={styles.imageContainer}>
          <img src={imageUrl} />
        </div>

        <div className={styles.infoContainer}>
          <div className={styles.title} title={title}>
            <a href={itemDetailUrl} target="_blank">
              {title}
            </a>
          </div>
          <span>商品id:{itemId}</span>
        </div>
      </div>
    );
  };

  const handlePaginationChange = (current, pageSize) => {
    setPagination({ ...pagination, current, pageSize });
  };

  const renderTab = () => {
    return (
      <Tab
        shape="text"
        tabPosition="top"
        activeKey={searchParams.auditStatus}
        style={{ backgroundColor: '#fff' }}
        navStyle={{ height: '45px' }}
        onChange={(value) => {
          const newParams = { ...searchParams, auditStatus: value, ...searchFormRef?.current?.getValues() };
          setSearchParams(newParams);
        }}
        extra={isPendingAudit ? <Button type="primary" onClick={handleBatchAudit}>批量审核</Button> : null}
      >
        {countList.map((countItem) => (
          <Tab.Item
            title={
              <Button text type={searchParams.auditStatus === countItem.type ? 'primary' : 'normal'}>
                {
                  auditStatusOptions
                    ?.concat([{ value: 'all', label: '全部', color: 'gray' }])
                    .find((item) => item.value === countItem?.type)?.label
                }
                ({countItem.count})
              </Button>
            }
            key={countItem.type}
          />
        ))}
      </Tab>
    );
  };

  const renderTable = () => {
    return (
      <Table
        loading={loading}
        dataSource={dataSource}
        primaryKey="itemId"
        fixedHeader
        maxBodyHeight={700}
        emptyContent={dataSource?.length === 0 && !loading ? '暂无商品' : ''}
        rowSelection={isPendingAudit ? {
          mode: 'multiple',
          getProps: (record) => ({ disabled: !record?.operateList?.includes('audit')}),
          onChange: (selectedRowKeys, records) => setSelectedRows(records),
        } : null as any}
      >
        <Table.Column
          title="商品信息"
          dataIndex="imageUrl"
          cell={(value: any, index: number, record: any) => renderImageInfo(record)}
          lock="left"
          width={290}
        />
        <Table.Column title="品牌" dataIndex="brandName" width={100} />
        <Table.Column title="叶子类目名称" dataIndex="leafCategoryName" width={120} />
        <Table.Column
          title="商家名称"
          dataIndex="sellerName"
          width={120}
          cell={(value) => <div className={styles.ellipsisSixLine}>{value}</div>}
        />
        <Table.Column title="市场价" dataIndex="price" width={130} />
        <Table.Column title="普惠券后价" dataIndex="egoReferencePrice" width={130} />
        <Table.Column title="批发价" dataIndex="egoPrice" width={130} />
        <Table.Column
          title="发货时效"
          dataIndex="shipTime"
          width={130}
          cell={(value) => {
            if (value) {
              return ` ${value}小时`;
            }
            return '';
          }}
        />
        <Table.Column title="库存" dataIndex="quantity" width={80} />
        <Table.Column
          title="商家税率税编"
          dataIndex="taxRate"
          width={120}
          cell={(value, index, record) => {
            return (
              <>
                <p>{value}</p>
                <p>{record?.taxCode}</p>
              </>
            );
          }}
        />
        <Table.Column
          title="企业购税率税编"
          dataIndex="egoTaxRate"
          width={120}
          cell={(value, index, record) => {
            if (value === '-1.0' || (value === undefined && record.applyStatus === 'processing')) {
              return <p>获取中</p>;
            }
            const style = record.taxRate !== record.egoTaxRate ? { color: 'red' } : {};
            return (
              <>
                <p style={style}>{value}</p>
                <p>{record?.egoTaxCode}</p>
              </>
            );
          }}
        />
        <Table.Column
          title="创建时间"
          dataIndex="gmtCreate"
          width={180}
          cell={formatTimestamp}
        />
        <Table.Column
          title="操作时间"
          dataIndex="gmtModified"
          width={180}
          cell={formatTimestamp}
        />
        <Table.Column
          title="操作者"
          dataIndex="updateOperator"
          width={120}
        />
        <Table.Column
          title="报名状态"
          dataIndex="applyStatus"
          width={120}
          lock="right"
          cell={(value, index, record) => {
            if (record.applyStatus === 'init' || !record.applyStatus) {
              return null;
            }
            const status = applyStatusOptions.find((item) => item.value === record.applyStatus);
            return (
              <div className={styles.statusContainer}>
                <Tag size="small" color={status?.color}>
                  {status?.label}
                </Tag>
                {record.applyStatus === 'fail' && record?.applyFailReason && (
                  <Balloon.Tooltip trigger={<Icon type="help" size="small" style={{ cursor: 'pointer' }} />} align="t">
                    {record.applyFailReason}
                  </Balloon.Tooltip>
                )}
              </div>
            );
          }}
        />
        <Table.Column
          title="审核状态"
          dataIndex="auditStatus"
          width={120}
          lock="right"
          cell={(value, index, record) => {
            if (record.auditStatus === 'init' || !record.auditStatus) {
              return null;
            }
            const auditStatus = auditStatusOptions.find((item) => item.value == record.auditStatus);
            return (
              <div className={styles.statusContainer}>
                <Tag size="small" color={auditStatus?.color}>
                  {auditStatus?.label}
                </Tag>
                {record.auditStatus === 'fail' && record.rejectReason && (
                  <Balloon.Tooltip trigger={<Icon type="help" size="small" style={{ cursor: 'pointer' }} />} align="t">
                    {record.rejectReason}
                  </Balloon.Tooltip>
                )}
              </div>
            );
          }}
        />
        <Table.Column title="操作" cell={renderActions} lock="right" width={160} />
      </Table>
    );
  };

  const renderPagination = () => {
    return (
      <div>
        <Pagination
          {...pagination}
          onChange={(current) => handlePaginationChange(current, pagination.pageSize)}
          onPageSizeChange={(size) => handlePaginationChange(1, size)}
          className={styles.myPagination}
          type="normal"
          shape="normal"
          showJump
          pageSizeSelector="dropdown"
          pageSizeList={[10, 20]}
          totalRender={(total) => <span className={styles.totalText}> 共 {total} 条</span>}
        />
      </div>
    );
  };

  return (
    <div className={styles.itemPoolManage}>
      <SearchForm ref={searchFormRef} onSearch={handleSearch} onReset={handleRefresh} />
      {renderTab()}
      {renderTable()}
      {renderPagination()}
      <AuditDialog
        visible={isAuditDialogVisible}
        onClose={() => {
          setIsAuditDialogVisible(false);
          setSelectedRows([]);
          setCurrentItemData(null);
        }}
        currentItemData={currentItemData}
        selectedRows={selectedRows}
        handleSearch={handleSearch}
      />
      <SkuDetail
        mode="view"
        visible={isSkuDetailVisible}
        onClose={() => {
          setIsSkuDetailVisible(false);
          setCurrentItemData(null);
        }}
        currentItemData={currentItemData}
      />
    </div>
  );
};

export default ItemPoolManage;
