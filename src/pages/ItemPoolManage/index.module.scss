.itemPoolManage {
  padding: 10px;
  background-color: #f0f4f7;
  min-height: 100vh;

  .next-form-item {
    margin-bottom: 10px;

    .next-form-item-control {
      .next-select,
      .next-datePicker2 {
        width: 100%;
        input {
          width: 100%;
        }
      }
    }
  }

  .searchBtnsButton {
    margin-right: 5px;
    margin-right: 10px;
  }

  .rcodeSearchBtns {
    display: flex;
    align-items: center;
  }

  .searchSection {
    background-color: #fff;
    margin-top: 20px;
    margin-bottom: 20px;
    padding: 8px;
  }

  .ellipsisSixLine {
    display: -webkit-box;
    -webkit-line-clamp: 6;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .tabRightextraButton {
    margin-right: 5px;
    margin-left: auto;
  }

  .statusContainer {
    display: flex;
    align-items: center;
    flex-direction: row;
    gap: 5px;
  }

  .imageInfoContainer {
    display: flex;

    .imageContainer {
      width: 96px;
      height: 96px;
      border-radius: 6px;

      img {
        width: 96px;
        height: 96px;
        border-radius: 6px;
      }
    }

    .infoContainer {
      margin-left: 13px;

      .title {
        height: 44px;
        font-family: PingFangSC;
        font-weight: 500;
        font-size: 14px;
        color: #4f89ff;
        letter-spacing: 0;
        line-height: 22px;
        margin-bottom: 16px;
        // 最多两行超出省略
        display: -webkit-box;
        -webkit-box-orient: vertical;
        line-clamp: 2;
        overflow: hidden;
      }

      span {
        font-family: PingFangSC;
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        letter-spacing: 0;
        line-height: 22px;
      }
    }
  }

  .myPagination {
    display: flex;
    justify-content: flex-end;
    margin: 16px 0;
    .totalText {
      margin-left: 16px;
    }
    .nextPaginationPages {
      margin-left: auto;
    }
  }
}
