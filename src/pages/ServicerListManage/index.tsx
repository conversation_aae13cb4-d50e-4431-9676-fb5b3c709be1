import React, { useState, useEffect } from 'react';
import { Table, Pagination, Button } from '@alifd/next';
import ServicerDrawer from './servicerDrawer';
import { queryServicerList } from '@/services';
import { IQueryServicerListParams, ServicerItem, queryServicerListResult } from '@/types';

import styles from './index.module.scss'; 
import { servicerListMockData } from '@/pages/Mock';

const ServicerList = () => {
  const [visible, setVisible] = useState(false);
  const [currentServicer, setCurrentServicer] = useState<ServicerItem | null>(null);
  const [mode, setMode] = useState('edit');
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<ServicerItem[]>([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0,
  });

  const columns = [
    {
      title: '服务商ID',
      dataIndex: 'sellerId',
      width: 100,
    },
    {
      title: '服务商名称',
      dataIndex: 'shortName',
      width: 120,
    },
    {
      title: '合同ID',
      dataIndex: 'contract.contractId',
      width: 100,
    },
    {
      title: '佣金比率',
      dataIndex: 'contract.contractId',
      width: 100,
    },
    {
      title: '创建时间',
      dataIndex: 'gmtCreate',
      width: 160,
    },
    {
      title: '最近编辑时间',
      dataIndex: 'gmtModified',
      width: 160,
    },
    {
      title: '最近一次编辑者',
      dataIndex: 'updateOperator',
      width: 120,
    },
    {
      title: '服务商状态',
      dataIndex: 'status',
      width: 100,
    },
    {
      title: '审核状态',
      dataIndex: 'effectiveStatus',
      width: 100,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 100,
      cell: (value: any, index: number, record: any) => (
        <>
          <Button
            text
            type="primary"
            style={{ marginRight: 10 }}
            onClick={() => {
              setCurrentServicer(record);
              setMode('edit');
              setVisible(true);
            }}
          >
            编辑
          </Button>
          <Button
            text
            type="primary"
            onClick={() => {
              setCurrentServicer(record);
              setMode('view');
              setVisible(true);
            }}
          >
            查看
          </Button>
        </>
      ),
    },
  ];

  const handleClose = () => {
    setVisible(false);
    setCurrentServicer(null);
  };

  const fetchServicerList = async (params?: IQueryServicerListParams) => {
    try {
      setLoading(true);
      const { current, pageSize } = pagination;
      const response = await queryServicerList({
        currentPage: current,
        pageSize: pageSize,
        ...params,
      });
      setDataSource(servicerListMockData || response.data?.dataList || []);
      setPagination({
        ...pagination,
        total: response.data?.totalCount || 0,
      });
    } catch (error) {
      console.error('获取服务商列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchServicerList();
  }, [pagination.current, pagination.pageSize]);

  const handleSave = async (values: any) => {
    try {
      handleClose();
      fetchServicerList();
    } catch (error) {
      console.error('保存失败:', error);
    }
  };

  const renderTable = () => {
    return (
      <div className={styles.cardServicerList}>
        <div className={styles.cardTitleContainer}>
          <span className={styles.cardTitle}>服务商列表</span>
          <Button
            className={styles.cardEnterpriseListButton}
            type="primary"
            onClick={() => {
              setMode('add');
              setVisible(true);
            }}
          >
            新增服务商
          </Button>
        </div>
        <div className={styles.listContainer}>
          <Table loading={loading} dataSource={dataSource} columns={columns} />
        </div>
      </div>
    );
  };

  const renderPagination = () => {
    return (
      <div>
        <Pagination
          total={pagination.total}
          current={pagination.current}
          pageSize={pagination.pageSize}
          className={styles.myPagination}
          type="normal"
          shape="normal"
          showJump
          pageSizeSelector="dropdown"
          pageSizeList={[5, 10, 20]}
          totalRender={(total) => <span className={styles.totalText}> 共 {total} 条</span>}
          onChange={(current) => {
            setPagination({ ...pagination, current });
          }}
          onPageSizeChange={(pageSize) => {
            setPagination({ ...pagination, pageSize, current: 1 });
          }}
        />
      </div>
    );
  };

  return (
    <div className={styles.servicerManage}>
      {renderTable()}
      {renderPagination()}
      <ServicerDrawer mode={mode} data={currentServicer} visible={visible} onClose={handleClose} onSave={handleSave} />
    </div>
  );
};

export default ServicerList;
