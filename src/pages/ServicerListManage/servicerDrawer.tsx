import React, { useEffect } from 'react';
import { Drawer, Input, DatePicker2, NumberPicker, Form, Field, Button, Radio } from '@alifd/next';

import { addServicer, queryServicerDetail } from '@/services';
import { IQueryServicerDetailParams, queryServicerDetailResult, ServicerItemDetail } from '@/types';
import { servicerDetailMockData } from '@/pages/Mock';

const servicerDrawer = ({ mode, data, visible, onClose }) => {
  const isEditMode = mode === 'edit';
  const isViewMode = mode === 'view';
  const fields = Field.useField({});

  const formItemLayout = {
    labelCol: {
      fixedSpan: 8,
    },
    wrapperCol: {
      span: 16,
    },
  };

  useEffect(() => {
    if (visible && (isEditMode || isViewMode) && data?.servicerId) {
      queryServicerDetail({ servicerId: data.sellerId }).then((res) => {
        const servicerData = servicerDetailMockData || res.data;
        fields.setValues({
          ...servicerData,
          contractId: servicerData?.contract?.contractId,
          contractName: servicerData?.contract?.contractName,
          contractPeriod: [servicerData?.contract?.startDate, servicerData?.contract?.endDate],
          saleServiceRate: servicerData?.settle?.saleServiceRate,
        });
      });
    }
  }, [visible, data, isEditMode, isViewMode]);

  const handleSubmit = async () => {
    try {
      const values = (await fields.validate()) as any;
      const submitData: Partial<ServicerItemDetail> = {
        sellerId: values?.sellerId,
        shopName: values?.shopName,
        contract: {
          contractId: values?.contractPlatformId,
          contractName: values?.contractName,
          startDate: values?.contractValidityPeriod?.[0],
          endDate: values?.contractValidityPeriod?.[1],
        },
        settle: {
          rebateRate: String(values?.rebateRatio),
          billCycle: values?.orderSettleStatus?.days,
          billCycleType: values?.orderSettleStatus?.type,
          settleCycle: values?.settleTime?.day,
          settleCycleType: values?.settleTime?.type,
        },
        invoice: {
          idenNumber: values?.taxpayerId,
          title: values?.invoiceTitle,
        },
      };
      addServicer(submitData).then((res) => {
        if (res.code === 0) {
          onClose();
        }
      });
    } catch (err) {
      console.error('Form validation failed:', err);
    }
  };

  return (
    <Drawer
      v2
      title={isViewMode ? '查看服务商' : isEditMode ? '编辑服务商' : '新建服务商'}
      visible={visible}
      onClose={onClose}
      width={1000}
    >
      <Form
        field={fields}
        {...formItemLayout}
        isPreview={isViewMode}
        useLabelForErrorMessage
        labelAlign="left"
      >
        <div className="form-item-group">
          <div className="group-title">合同信息</div>
          <div className="grid-container">
            <Form.Item
              name="shortName"
              label="服务商名称"
              required={true}
              labelCol={{ fixedSpan: 6 }}
              labelAlign="left"
              colon
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="servicerId"
              label="服务商id（集采）"
              labelCol={{ fixedSpan: 6 }}
              labelAlign="left"
              colon
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="fullName"
              label="服务商公司全称"
              labelCol={{ fixedSpan: 6 }}
              labelAlign="left"
              colon
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="contractId"
              label="合同平台合同id"
              required={true}
              labelCol={{ fixedSpan: 6 }}
              labelAlign="left"
              colon
            >
              <Input 
                placeholder="请输入" 
                maxLength={20}
                onInput={(e: any) => {
                  // Remove any non-digit characters
                  let value = e.target.value.replace(/\D/g, '');
                  if (value.length > 20) {
                    value = value.slice(0, 20);
                  }
                  e.target.value = value;
                }} />
            </Form.Item>
            <Form.Item
              name="contractName"
              label="名称"
              required={true}
              help={
                <a href="" target="_blank">
                  查看合同
                </a>
              }
              labelCol={{ fixedSpan: 6 }}
              labelAlign="left"
              colon
            >
              <Input defaultValue="品恒服务商2025财年带客合同" />
            </Form.Item>
            <Form.Item
              name="contractPeriod"
              label="合同有效期"
              required={true}
              labelCol={{ fixedSpan: 6 }}
              labelAlign="left"
              colon
            >
              <DatePicker2.RangePicker />
            </Form.Item>
          </div>
        </div>
        <div className="form-item-group">
          <div className="group-title">结算信息</div>
          <div className="grid-container">
            <Form.Item
              name="billCycleType"
              label="结算方式"
              labelCol={{ fixedSpan: 6 }}
              required={true}
              labelAlign="left"
              colon
            >
              <Radio.Group shape="normal">
                <Radio key="0" value="followCustomer">
                  跟随企业客户进行结算
                </Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item
              label="注"
              labelCol={{ fixedSpan: 6 }}
              labelAlign="left"
              colon
            >
              <p className="label-content">{'企业客户订单（销售）结算后，服务商佣金才会结算'}</p>
            </Form.Item>
            <Form.Item
              name="saleServiceRate"
              label="佣金比率"
              labelCol={{ fixedSpan: 6 }}
              required={true}
              labelAlign="left"
              colon
            >
              <Input innerAfter="%" />
            </Form.Item>
          </div>
        </div>
        {!isViewMode && (
          <div
            style={{
              position: 'absolute',
              bottom: 0,
              width: '100%',
              borderTop: '1px solid #e8e8e8',
              padding: '10px 16px',
              textAlign: 'center',
              left: 0,
              background: '#fff',
              borderRadius: '0 0 4px 4px',
            }}
          >
            <Button type="primary" onClick={handleSubmit}>
              提交审核
            </Button>
          </div>
        )}
      </Form>
    </Drawer>
  );
};

export default servicerDrawer;
