import React from 'react';
import { useEffect } from 'react';
import { Drawer, Input, DatePicker2, Form, Field, Button, Message, Dialog, Select } from '@alifd/next';
import ReviewModal from '@/components/ReviewModal';
import OperationButton from '@/components/OperationButton';

import { addMerchant, queryMerchantDetail, queryTaobaoMerchantInfo, queryContractBaseInfo, submitMerchantAudit, auditAll, queryRateSellerLevelSelect } from '@/services';
import { MerchantItemDetail, queryMerchantDetailResult } from '@/types';

import styles from '@/pages/MerchantListManage/index.module.scss';
import { isTrue } from '@/pages/Utils';

interface MerchantDrawerProps {
  mode: 'edit' | 'view' | 'add';
  data?: any;
  visible: boolean;
  onClose: () => void;
  refresh: () => void;
}

const formItemLayout = {
  labelCol: {
    fixedSpan: 8,
  },
  wrapperCol: {
    span: 14,
  },
};

const MerchantDrawer: React.FC<MerchantDrawerProps> = ({ mode, data, visible, onClose, refresh }) => {
  const [reviewModalVisible, setReviewModalVisible] = React.useState(false);
  const [loading, setLoading] = React.useState(false);
  const [dataReady, setDataReady] = React.useState(false);
  const [rateSellerLevelOptions, setRateSellerLevelOptions] = React.useState([]);
  const isEditMode = mode === 'edit';
  const isViewMode = mode === 'view';
  const fields = Field.useField({});

  // 获取佣金优惠比率选项
  useEffect(() => {
    if (visible) {
      queryRateSellerLevelSelect({})
        .then((res) => {
          if (isTrue(res?.success) && res?.data) {
            const options = res.data.map((item: any) => ({
              label: item.label || item.name,
              value: item.value || item.code
            }));
            setRateSellerLevelOptions(options);
          }
        })
        .catch((error) => {
          console.error('Failed to fetch rate seller level options:', error);
        });
    }
  }, [visible]);

  useEffect(() => {
    fields.reset();
    setDataReady(false);
    if (visible && (isEditMode || isViewMode) && data?.roleId) {
      setLoading(true);
      queryMerchantDetail({ roleId: data.roleId })
        .then((res) => {
          const merchantData = res.data;
          fields.setValues({
            auditStatus: merchantData?.auditStatus,
            editVersion: merchantData?.editVersion,
            sellerId: merchantData?.outId,
            roleId: merchantData?.roleId,
            name: merchantData?.name,
            companyName: merchantData?.properties?.companyName,
            creditIdentifier: merchantData?.properties?.creditIdentifier,
            contractId: merchantData?.properties?.contractId,
            contractName: merchantData?.properties?.contractName,
            contractValidityPeriod: [Number(merchantData?.properties?.contractStartTime), Number(merchantData?.properties?.contractEndTime)],
            contractCompanyName: merchantData?.properties?.contractCompanyName,
            contractOurCompanyName: merchantData?.properties?.contractOurCompanyName,
            rateSellerLevel: merchantData?.properties?.rateSellerLevel || '',
            operationList: merchantData?.operationList || []
          });
          merchantData && setDataReady(true);
          // merchantData &&  handleContractIdChange();
        })
        .finally(() => {
          setLoading(false);
        });
    } else if (mode === 'add') {
      setDataReady(true);
    }
  }, [visible, data, isEditMode, isViewMode, mode]);

  const handleSave = async ({ skipSuccessMessage = false }) => {
    try {
      const result = await fields.validatePromise();
      if (result?.errors) {
        return;
      }
      const values = fields.getValues() as any;
      const submitData: Partial<MerchantItemDetail> = {
        "name": values.name as string,
        "editVersion": mode === 'edit' ? values.editVersion : 0,
        "outId": values.sellerId,
        ...(values.roleId ? { roleId: values.roleId } : {}),
        "properties": {
          "companyName": values?.companyName,
          "creditIdentifier": values?.creditIdentifier,
          "contractId": values?.contractId,
          "contractName": values?.contractName,
          "contractCompanyName": values?.contractCompanyName,
          "contractOurCompanyName": values?.contractOurCompanyName,
          "contractStartTime": values?.contractValidityPeriod[0],
          "contractEndTime": values?.contractValidityPeriod[1],
          "rateSellerLevel": values?.rateSellerLevel,
        }
      }
      const res = await addMerchant(submitData as queryMerchantDetailResult);
      if (isTrue(res?.success)) {
        if (!skipSuccessMessage) {
          Dialog.success({
            title: "提示",
            content: mode === 'edit' ? '编辑商家成功' : '新增商家成功',
          });
          refresh();
          onClose();
        }
        return res;
      } else {
        Dialog.error({
          title: "提示",
          content: res?.message || '操作失败',
        });
        return Promise.reject(res?.message || '操作失败');
      }
    } catch (err) {
      console.error('Form validation failed:', err);
      return Promise.reject(err);
    }
  };

  const handleQueryTaobaoMerchantInfo = async () => {
    try {
      const sellerId = fields.getValue('sellerId');
      if (!sellerId) {
        return;
      }
      // Query Taobao merchant info
      const merchantInfo = await queryTaobaoMerchantInfo({ sellerId: Number(sellerId) });
      if (isTrue(merchantInfo?.success)) {
        fields.setValues({
          name: merchantInfo?.data?.sellerName || '',
          companyName: merchantInfo?.data?.companyName || '',
          creditIdentifier: merchantInfo?.data?.creditIdentifier || '',
        });
        // @ts-ignore
        fields.validate(['name', 'companyName', 'creditIdentifier']);
      } else {
        Dialog.error({
          title: "提示",
          content: merchantInfo?.message || '查询商家信息失败',
        });
      }
    } catch (error) {
      console.error('Failed to query Taobao merchant info:', error);
    }
  };

  const handleContractIdChange = async () => {
    const contractId = fields.getValue('contractId');
    if (!contractId) return;
    try {
      const contractInfo = await queryContractBaseInfo({ contractId: contractId as string });
      if (isTrue(contractInfo?.success)) {
        fields.setValues({
          contractName: contractInfo.data?.contractName || '',
          contractValidityPeriod: [
            contractInfo.data?.startDate ? contractInfo.data.startDate : null,
            contractInfo.data?.endDate ? contractInfo.data.endDate : null
          ],
          contractCompanyName: contractInfo.data?.otherCompanyName || '',
          contractOurCompanyName: contractInfo.data?.ourCompanyName || '',
          detailUrl: contractInfo.data?.detailUrl || '',
        });
        // @ts-ignore
        fields.validate(['contractName', 'contractValidityPeriod', 'contractCompanyName']);
      } else {
        Dialog.error({
          title: "提示",
          content: contractInfo?.message || '查询合同信息失败',
        });
      }
    } catch (error) {
      console.error('Failed to query contract info:', error);
    }
  };

  const handleSubmitAudit = async () => {
    try {
      // First validate the form
      const result = await fields.validatePromise();
      if (result?.errors) {
        return;
      }

      const saveResponse = await handleSave({ skipSuccessMessage: true });

      const values = fields.getValues();
      let roleId = values?.roleId;

      if (saveResponse?.data?.roleId) {
        roleId = saveResponse?.data?.roleId;
      }
      // Query the latest merchant information
      const merchantDetail = await queryMerchantDetail({ roleId: Number(roleId) });
      const data = merchantDetail?.data;

      if (!data) {
        Dialog.error({
          title: "提示",
          content: '获取商家信息失败',
        });
        return;
      }

      // Update form with latest data
      fields.setValues({
        roleId: data?.roleId,
        editVersion: data?.editVersion,
      });

      // Submit for audit with updated information
      const res = await submitMerchantAudit({
        roleId: Number(data?.roleId),
        editVersion: Number(data?.editVersion),
      });

      if (isTrue(res?.success)) {
        Dialog.success({
          title: "提示",
          content: <div>
            提交审核成功，
            <a href={res?.data?.auditUrl} target="_blank" rel="noopener noreferrer">
               查看审批流
            </a>
          </div>
        });
        onClose();
        setTimeout(() => {
          refresh();
        }, 1000);
      } else {
        Dialog.error({
          title: "提示",
          content: res?.message || '提交审核失败',
        });
      }
    } catch (err) {
      console.error('Submit audit failed:', err);
      // Dialog.error({
      //   title: "提示",
      //   content: '提交审核失败',
      // });
    }
  };

  const handleCancelAudit = async () => {
    try {
      const values = fields.getValues();
      const res = await auditAll({
        instanceType: 'bpms',
        targetType: 'seller',
        auditType: 'cancel',
        targetIdList: [String(values?.roleId)],
      });
      if (isTrue(res?.success)) {
        Message.success('操作已提交,审核状态有一定延迟,请以bpms页面结果为准。');
        setTimeout(() => {
          refresh();
        }, 1000);
        onClose();
      } else {
        Message.error(res?.message || '撤回审核失败');
      }
    } catch (err) {
      console.error('Cancel audit failed:', err);
      Message.error('撤回审核失败');
    }
  };

  const handleApproveAudit = async (comment: string) => {
    try {
      const values = fields.getValues();
      const res = await auditAll({
        instanceType: 'bpms',
        targetType: 'seller',
        auditType: 'approve',
        targetIdList: [String(values?.roleId)],
      });
      if (isTrue(res?.success)) {
        Message.success('操作已提交,审核状态有一定延迟,请以bpms页面结果为准。');
        setTimeout(() => {
          refresh();
        }, 1000);
        onClose();
      } else {
        Message.error(res?.message || '审核通过失败');
      }
    } catch (err) {
      console.error('Approve audit failed:', err);
      Message.error('审核通过失败');
    }
  };

  const handleRejectAudit = async (comment: string) => {
    try {
      const values = fields.getValues();
      const res = await auditAll({
        instanceType: 'bpms',
        targetType: 'seller',
        auditType: 'reject',
        targetIdList: [String(values?.roleId)],
        mark: comment,
      });
      if (isTrue(res?.success)) {
        Message.success('操作已提交,审核状态有一定延迟,请以bpms页面结果为准。');
        setTimeout(() => {
          refresh();
        }, 1000);
        onClose();
      } else {
        Message.error(res?.message || '审核拒绝失败');
      }
    } catch (err) {
      console.error('Reject audit failed:', err);
      Message.error('审核拒绝失败');
    }
  };

  const renderActionButtons = () => {
    const operationList = fields.getValue('operationList') || [];
    const operationHandlers = {
      'save': handleSave,
      'submit': handleSubmitAudit,
      'cancel': handleCancelAudit,
      'audit': () => setReviewModalVisible(true)
    };
    if (mode === 'add') {
      return (
        <div className={styles.formSubmitButtonContainer}>
          {['save', 'submit']?.map((operation: string) => {
            return (
              <OperationButton
                key={operation}
                operation={operation}
                onClick={operationHandlers[operation]}
              />
            );
          })}
        </div>
      );
    }

    return (
      <div className={styles.formSubmitButtonContainer}>
        {operationList.map((_operation: string) => {
          const operation = _operation.toLowerCase();
          return (
            <OperationButton
              key={operation}
              operation={operation}
              onClick={operationHandlers[operation]}
            />
          );
        })}
      </div>
    );
  };

  return (
    <>
      <Drawer
        v2
        title={isViewMode ? '查看商家' : isEditMode ? '编辑商家' : '新建商家'}
        visible={visible && dataReady}
        onClose={onClose}
        width={1000}
      >
        <Form
          field={fields}
          {...formItemLayout}
          isPreview={isViewMode}
          fullWidth
          className={styles.merchantForm}
          useLabelForErrorMessage
        >
          {loading ? (
            <div style={{ textAlign: 'center', padding: '20px' }}>加载中...</div>
          ) : (
            <>
              <div className={styles.formItemGroup}>
                <div className={styles.groupTitle}>商家信息</div>
                <Form.Item name="sellerId" label="商家sellerId" required={true} colon  disabled={isEditMode || isViewMode}>
                <Input
                    placeholder="请输入商家sellerId"
                    className={styles.inputWidth}
                    maxLength={18}
                    onInput={(e: any) => {
                      // Remove any non-digit characters and limit to 20 digits
                      let value = e.target.value.replace(/\D/g, '');
                      if (value.length > 18) {
                        value = value.slice(0, 18);
                      }
                      e.target.value = value;
                    }} />
                  {mode === 'add' && <Button type="primary" style={{ marginLeft: 10 }} text onClick={handleQueryTaobaoMerchantInfo}>查询</Button>}
                </Form.Item>
                <Form.Item name="name" label="商家名称" required={true} colon isPreview>
                  <Input className={styles.inputWidth} placeholder="品牌企业商店铺" />
                </Form.Item>
                <Form.Item name="companyName" label="企业名称" required={true} colon isPreview>
                  <Input className={styles.inputWidth} placeholder="品牌商有限公司" />
                </Form.Item>
                <Form.Item name="creditIdentifier" label="统一社会信用代码" required={true} colon isPreview>
                  <Input className={styles.inputWidth} placeholder="" />
                </Form.Item>
              </div>

              <div className={styles.formItemGroup}>
                <div className={styles.groupTitle}>合同信息</div>
                <Form.Item name="contractId"
                  label="合同平台合同Id"
                  required={true}
                  disabled={isViewMode}
                  colon
                  help={
                    mode === 'view' || !fields?.getValue('detailUrl') ? null :
                      <a href={fields?.getValue('detailUrl')} target="_blank" style={{ fontSize: '12px' }}>
                        查看合同
                      </a>
                  }
                  >
                  <Input
                    placeholder="请输入合同平台合同Id"
                    className={styles.inputWidth}
                    maxLength={18}
                    onInput={(e: any) => {
                      // Remove any non-digit characters and limit to 19 digits
                      let value = e.target.value.replace(/\D/g, '');
                      if (value.length > 18) {
                        value = value.slice(0, 18);
                      }
                      e.target.value = value;
                    }} />
                  {(mode === 'add' ||  isEditMode) && <Button type="primary" style={{ marginLeft: 10 }} text onClick={handleContractIdChange}>查询</Button>}
                </Form.Item>
                <Form.Item name="contractName" label="名称" required={true} colon isPreview>
                  <Input className={styles.inputWidth} placeholder="" isPreview />
                </Form.Item>
                <Form.Item name="contractValidityPeriod" label="合同有效期" required={true} colon isPreview>
                  <DatePicker2.RangePicker
                    className={styles.inputWidth}
                    style={{ width: '100%' }}
                    placeholder={['请选择时间', '请选择时间']}
                    format="YYYY-MM-DD HH:mm:ss"
                    // @ts-ignore
                    showTime={{
                      defaultValue: ['00:00:00', '23:59:59'],
                    }}
                  />
                </Form.Item>
                <Form.Item name="contractOurCompanyName" label="签约主体(我方)" required={true} colon isPreview>
                  <Input className={styles.inputWidth} placeholder="" />
                </Form.Item>
                <Form.Item name="contractCompanyName" label="签约主体(对方)" required={true} colon isPreview>
                  <Input className={styles.inputWidth} placeholder="" />
                </Form.Item>
              </div>

              <div className={styles.formItemGroup}>
                <div className={styles.groupTitle}>结算信息</div>
                <Form.Item
                  name="rateSellerLevel"
                  label="佣金优惠比率"
                  required={true}
                  colon
                  isPreview={isViewMode}
                  help="在原有费率基础减x%"
                  preferMarginToDisplayHelp
                >
                  <Select
                    className={styles.inputWidth}
                    placeholder="请选择佣金优惠比率"
                    dataSource={rateSellerLevelOptions}
                  />
                </Form.Item>
              </div>
              {renderActionButtons()}
            </>
          )}
        </Form>
      </Drawer>
      <ReviewModal
        visible={reviewModalVisible}
        onClose={() => setReviewModalVisible(false)}
        onApprove={handleApproveAudit}
        onReject={handleRejectAudit}
        modalTitle="商家审核"
      />
    </>
  );
};

export default MerchantDrawer;
