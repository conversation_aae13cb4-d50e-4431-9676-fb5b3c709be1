import React from 'react';
import { Pagination } from '@alifd/next';
import styles from '@/pages/MerchantListManage/index.module.scss';

interface MerchantPaginationProps {
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };
  onChange: (current: number) => void;
  onPageSizeChange: (pageSize: number) => void;
}

const MerchantPagination: React.FC<MerchantPaginationProps> = ({
  pagination,
  onChange,
  onPageSizeChange,
}) => {
  return (
    <div>
      <Pagination
        total={pagination.total}
        current={pagination.current}
        pageSize={pagination.pageSize}
        className={styles.myPagination}
        type="normal"
        shape="normal"
        showJump
        pageSizeSelector="dropdown"
        pageSizeList={[ 10, 20]}
        totalRender={(total) => <span className={styles.totalText}> 共 {total} 条</span>}
        onChange={onChange}
        onPageSizeChange={onPageSizeChange}
      />
    </div>
  );
};

export default MerchantPagination; 