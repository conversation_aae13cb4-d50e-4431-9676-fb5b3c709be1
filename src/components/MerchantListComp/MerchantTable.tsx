import React from 'react';
import AdaptiveTable from '@/components/AdaptiveTable';
import { <PERSON>ton, Dialog } from '@alifd/next';
import { MerchantItem } from '@/types';
import styles from '@/pages/MerchantListManage/index.module.scss';
import { formatTimestamp } from '@/pages/Utils';
import StatusTag from '@/components/EnterpriseManageComp/StatusTag';
import ListStatusTag from '@/components/ListStatusTag';

interface MerchantTableProps {
    loading: boolean;
    dataSource: MerchantItem[];
    onEdit: (record: MerchantItem) => void;
    onView: (record: MerchantItem) => void;
    onAdd: () => void;
    onDelete: (record: MerchantItem) => void;
    onInvalid: (record: MerchantItem) => void;
}

const MerchantTable: React.FC<MerchantTableProps> = ({
    loading,
    dataSource,
    onEdit,
    onView,
    onAdd,
    onDelete,
    onInvalid,
}) => {
    const columns = [
        {
            title: 'sellerId',
            dataIndex: 'outId',
            width: 100,
            lock: 'left',
        },
        {
            title: '商家名称',
            dataIndex: 'name',
            width: 120,
        },
        {
            title: '企业名称',
            dataIndex: 'properties.companyName',
            width: 200,
        },
        {
            title: '创建时间',
            dataIndex: 'gmtCreate',
            width: 160,
            cell: formatTimestamp
        },
        {
            title: '最近编辑时间',
            dataIndex: 'gmtModified',
            width: 160,
            cell: formatTimestamp
        },
        {
            title: '最近一次编辑者',
            dataIndex: 'updateOperator',
            width: 120,
        },
        {
            title: '商家状态',
            dataIndex: 'status',
            width: 100,
            lock: 'right',
            cell: (value: any, index: number, record: any) => {
                return <ListStatusTag status={record?.status} />
            }
        },
        {
            title: '审核状态',
            dataIndex: 'auditStatus',
            width: 100,
            lock: 'right',
            cell: (value: any, index: number, record: any) => {
                return <StatusTag value={record?.auditStatus} record={record} />
            }
        },
        {
            title: '操作',
            dataIndex: 'operation',
            width: 140,
            lock: 'right',
            cell: (value: any, index: number, record: MerchantItem) => {
                const operationList = record?.operationList || [];
                const operations = [
                    {
                        key: 'EDIT',
                        text: '编辑',
                        onClick: () => onEdit(record)
                    },
                    {
                        key: 'INVALIDATE',
                        text: '失效',
                        onClick: () => {
                            Dialog.confirm({
                                title: '确认',
                                content: '失效后不可恢复，是否确认操作？',
                                onOk: () => onInvalid(record),
                            });
                        }
                    },
                    {
                        key: 'DELETE',
                        text: '删除',
                        onClick: () => {
                            Dialog.confirm({
                                title: '确认',
                                content: '确定要删除该商家吗？',
                                onOk: () => onDelete(record),
                            });
                        }
                    },
                    {
                        key: 'VIEW',
                        text: '查看',
                        onClick: () => onView(record)
                    }
                ];

                return (
                    <div>
                        {operations.map((op) => 
                            operationList.includes(op.key) && (
                                <Button
                                    key={op.key}
                                    type="primary"
                                    text
                                    onClick={op.onClick}
                                    style={{ marginRight: '10px' }}
                                >
                                    {op.text}
                                </Button>
                            )
                        )}
                    </div>
                );
            },
        },
    ];

    return (
        <div className={styles.cardMerchantList}>
            <div className={styles.cardTitleContainer}>
                <span className={styles.cardTitle}>商家列表</span>
                <Button
                    className={styles.cardEnterpriseListButton}
                    type="primary"
                    onClick={onAdd}
                >
                    新增商家
                </Button>
            </div>
            <div className={styles.listContainer}>
                <AdaptiveTable
                    loading={loading}
                    dataSource={dataSource}
                    columns={columns}
                    primaryKey="outId"
                    emptyContent={<div> 暂无数据 </div>}
                    adaptiveThreshold={1200}
                />
            </div>
        </div>
    );
};

export default MerchantTable; 