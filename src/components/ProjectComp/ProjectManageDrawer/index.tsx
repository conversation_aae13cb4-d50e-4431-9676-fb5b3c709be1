import React, { useState, useEffect } from 'react';
import { Input, Select, DatePicker2, NumberPicker, Grid, Form, Field, Drawer, Message, Dialog } from '@alifd/next';
import OperationButton from '@/components/OperationButton';
import {
  queryProjectDetail, addProject, submitProjectAudit, editProject,
  queryContractBaseInfo, queryEnterpriseSelectList, queryEnterpriseSelectListExtend,
  auditAll
} from '@/services';
import ReviewModal from '../../../components/ReviewModal';

import styles from './index.module.scss';
import { validateEmail, validatePhoneNumber } from '@/pages/Utils';
import { isTrue } from '@/pages/Utils';


const titleMap = { add: '新建项目', edit: '编辑项目', view: '查看项目' };

interface ProjectManageDrawerProps {
  enterpriseId: number;
  mode: 'edit' | 'add' | 'view';
  projectId?: number;
  visible: boolean;
  onClose: () => void;
  refresh: () => void;
}

const ProjectManageDrawer: React.FC<ProjectManageDrawerProps> = ({ visible, onClose, projectId, mode, refresh, enterpriseId }) => {
  const fields = Field.useField();
  const [enterpriseOptions, setEnterpriseOptions] = useState([]);
  const [enterpriseExtendOptions, setEnterpriseExtendOptions] = useState<any>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isDataLoaded, setIsDataLoaded] = useState(false);
  const drawerTitle = titleMap[mode];
  const [reviewModalVisible, setReviewModalVisible] = useState(false);

  const fetchEnterpriseExtendOptions = async (roleId: string) => {
    if (!roleId) {
      setEnterpriseExtendOptions([]);
      return;
    }
    const response = await queryEnterpriseSelectListExtend({ roleId });
    if (response && response.data) {
      setEnterpriseExtendOptions(response.data.map(item => ({
        value: item?.roleId,
        label: item?.name
      })));
    }
  };

  const handleEnterpriseChange = async (value: string) => {
    fields.setValue('invoiceSubjectList', []);
    await fetchEnterpriseExtendOptions(value);
  };

  useEffect(() => {
    if (projectId && visible) {
      setIsLoading(true);
      setIsDataLoaded(false);
      queryProjectDetail({ projectId: Number(projectId) }).then((data) => {
        const projectDetail = data?.data;
        if (projectDetail) {
          fields.setValues({
            operationList: projectDetail?.operationList,
            editVersion: projectDetail?.editVersion,
            projectId: projectDetail?.projectId,
            status: projectDetail?.status,
            projectName: projectDetail?.projectName,
            enterpriseId: projectDetail?.enterprise?.enterpriseId,
            contractId: projectDetail?.contract?.contractId,
            contractName: projectDetail?.contract?.contractName,
            contractPeriod: [
              projectDetail?.contract?.startDate,
              projectDetail?.contract?.endDate
            ],
            contractAmount: projectDetail?.contract?.contractAmount,
            creditAmount: projectDetail?.contract?.creditAmount,
            // 新增字段
            icServiceRate: projectDetail?.contract?.icServiceRate?.toString(),
            invoiceSubjectList: projectDetail?.invoiceSubjectList?.map(item => item?.enterpriseId),
            name: projectDetail?.contact?.name,
            phoneNumber: projectDetail?.contact?.phoneNumber,
            email: projectDetail?.contact?.email,
            address: projectDetail?.contact?.address,
            intervalCycle: projectDetail?.billInterval?.intervalCycle,
            intervalDate: projectDetail?.billInterval?.intervalDate,
            intervalCycle_settle: projectDetail?.settleInterval?.intervalCycle,
            intervalType_settle: projectDetail?.settleInterval?.intervalType,
            detailUrl: projectDetail?.contract?.detailUrl,
            otherCompanyName: projectDetail?.contract?.otherCompanyName,
            ourCompanyName: projectDetail?.contract?.ourCompanyName
          });
          if (projectDetail?.enterprise?.enterpriseId) {
            fetchEnterpriseExtendOptions(String(projectDetail.enterprise.enterpriseId));
          }
          projectDetail && setIsDataLoaded(true);
        }
      }).finally(() => {
        setIsLoading(false);
      });
    } else {
      setIsDataLoaded(mode === 'add');
    }
  }, [projectId, visible, fields, mode]);

  useEffect(() => {
    // Fetch enterprise options
    queryEnterpriseSelectList({ roleId: enterpriseId || null }).then(response => {
      if (response && response.data) {
        setEnterpriseOptions(response.data?.map(item => ({
          value: String(item?.roleId),
          label: item?.name
        })));
        enterpriseId && fields.setValue('enterpriseId', String(enterpriseId));
        enterpriseId && fetchEnterpriseExtendOptions(String(enterpriseId));
      }
    });
  }, []);

  const handleContractQuery = async (contractId: string, fields: any) => {
    if (!contractId) return;

    try {
      const result = await queryContractBaseInfo({ contractId });
      const contractInfo = result?.data;
      if (isTrue(result?.success)) {
        fields.setValues({
          contractName: contractInfo?.contractName,
          contractPeriod: [contractInfo?.startDate, contractInfo?.endDate],
          otherCompanyName: contractInfo?.otherCompanyName,
          ourCompanyName: contractInfo?.ourCompanyName,
          contractAmount: contractInfo?.contractAmount,
          detailUrl: contractInfo?.detailUrl,
        });
      } else {
        Dialog.error({
          title: '提示',
          content: result?.message || '查询合同失败'
        })
      }
    } catch (error) {
      console.error('Failed to fetch contract info:', error);
    }
  };

  const handleSaveWithOptions = async ({ skipSuccessMessage = false }: { skipSuccessMessage?: boolean } = {}) => {
    const result = await fields.validatePromise() as any;
    if (result?.errors) {
      return Promise.reject(result.errors);
    }
    
    const values = fields.getValues();
    const params = {
      invoiceSubjectList: values?.invoiceSubjectList,
      contract: {
        otherCompanyName: values?.otherCompanyName,
        ourCompanyName: values?.ourCompanyName,
        endDate: values?.contractPeriod?.[1],
        contractId: values?.contractId,
        contractAmount: values?.contractAmount,
        creditAmount: values?.creditAmount,
        icServiceRate : values?.icServiceRate,
        contractName: values?.contractName,
        detailUrl: values?.detailUrl,
        startDate: values?.contractPeriod?.[0]
      },
      contact: {
        phoneNumber: values?.phoneNumber,
        address: values?.address,
        name: values?.name,
        email: values?.email
      },
      settleInterval: {
        intervalCycle: values?.intervalCycle_settle,
        intervalType: values?.intervalType_settle
      },
      billInterval: {
        intervalCycle: values?.intervalCycle,
        intervalType: 'month',
        intervalDate: values?.intervalDate
      },
      enterpriseId: values?.enterpriseId,
      projectName: values?.projectName,
      editVersion: mode === 'add' ? 0 : values?.editVersion,
      projectId: mode === 'add' ? null : values?.projectId
    };

    try {
      const result = mode === 'add'
        ? await addProject(params)
        : await editProject(params);

      if (isTrue(result.success)) {
        if (!skipSuccessMessage) {
          Dialog.success({
            title: "提示",
            content: mode === 'add' ? '新增成功' : '编辑成功'
          });
          onClose();
          setTimeout(() => {
            refresh();
          }, 1000);
        }
        return result;
      } else {
        Dialog.error({
          title: "提示",
          content: result?.message || '操作失败'
        });
        return Promise.reject(result?.message || '操作失败');
      }
    } catch (error) {
      return Promise.reject(error);
    }
  };

  const handleSave = () => handleSaveWithOptions();

  const handleSubmitAudit = async () => {
    try {
      // First validate the form
      const validateResult = await fields.validatePromise() as any;
      if (validateResult?.errors) {
        return;
      }
      const saveResponse = await handleSaveWithOptions({ skipSuccessMessage: true });
      const values = fields.getValues();
      let projectId = values?.projectId;

      if (saveResponse?.data?.projectId) {
        projectId = saveResponse?.data?.projectId;
      }
      // Query the latest project information
      const projectDetail = await queryProjectDetail({ projectId: Number(projectId) });
      const data = projectDetail?.data;
      
      if (!data) {
        Message.error('获取项目信息失败');
        return;
      }
      
      // Update form with latest data
      fields.setValues({
        projectId: data?.projectId,
        editVersion: data?.editVersion,
      });
      
      // Submit for audit with updated information
      const result = await submitProjectAudit({
        projectId: Number(data?.projectId),
        editVersion: Number(data?.editVersion)
      });

      if (isTrue(result.success)) {
        Dialog.success({
          title: "提示",
          content: <div>
            提交审核成功，
            <a href={result?.data?.auditUrl} target="_blank" rel="noopener noreferrer">
              查看审批流
            </a>
          </div>
        });
        setTimeout(() => {
          refresh();
        }, 1000);
        onClose();
      } else {
        Message.error(result?.message || '提交审核失败');
      }
    } catch (error) {
      // Message.error(error?.message || '提交审核失败');
      console.error('Submit audit error:', error);
    }
  };

  const handleCancelAudit = async () => {
    try {
      const values = fields.getValues();
      const result = await auditAll({
        instanceType: 'bpms',
        targetType: 'project',
        auditType: 'cancel',
        targetIdList: [String(values?.projectId)],
      });

      if (isTrue(result.success)) {
        Message.success('操作已提交,审核状态有一定延迟,请以bpms页面结果为准。');
        setTimeout(() => {
          refresh();
        }, 1000);
        onClose();
      } else {
        Message.error(result?.message || '撤回审核失败');
      }
    } catch (error) {
      Message.error('撤回审核失败');
      console.error('Cancel audit error:', error);
    }
  };

  const handleApprove = async () => {
    try {
      const values = fields.getValues();
      const result = await auditAll({
        instanceType: 'bpms',
        targetType: 'project',
        auditType: 'approve',
        targetIdList: [String(values?.projectId)],
      });
      if (isTrue(result.success)) {
        Message.success('操作已提交,审核状态有一定延迟,请以bpms页面结果为准。');
        setTimeout(() => {
          refresh();
        }, 1000);
        onClose();
      } else {
        Message.error(result?.message || '审核通过失败');
      }
    } catch (error) {
      Message.error('审核通过失败');
      console.error('Approve error:', error);
    }
  };

  const handleReject = async (reason: string) => {
    try {
      const values = fields.getValues();
      const result = await auditAll({
        instanceType: 'bpms',
        targetType: 'project',
        auditType: 'reject',
        targetIdList: [String(values?.projectId)],
        mark: reason
      });

      if (isTrue(result.success)) {
        Message.success('操作已提交,审核状态有一定延迟,请以bpms页面结果为准。');
        setTimeout(() => {
          refresh();
        }, 1000);
        onClose();
      } else {
        Message.error(result?.message || '审核拒绝失败');
      }
    } catch (error) {
      Message.error('审核拒绝失败');
      console.error('Reject error:', error);
    }
  };

  // 添加获取月份最大天数的函数
  const getMaxDaysInMonth = (month: number) => {
    const thirtyDaysMonths = [4, 6, 9, 11];
    if (month === 2) {
      return 29; // 考虑闰年，这里取29
    } else if (thirtyDaysMonths.includes(month)) {
      return 30;
    }
    return 31;
  };

  // 添加月份变化处理函数
  const handleIntervalCycleChange = (value: number) => {
    const currentDate = fields.getValue('intervalDate');
    const maxDays = getMaxDaysInMonth(value);
    // 如果当前选择的日期大于该月最大天数，则自动调整为最大天数
    if (Number(currentDate) > maxDays) {
      fields.setValue('intervalDate', maxDays);
    }
  };

  const renderMyForm = () => {
    const formItemLayout = {
      labelCol: {
        fixedSpan: 6,
      },
      wrapperCol: {
        span: 14,
      },
    };

    return (
      <Form field={fields} {...formItemLayout} labelAlign="left" useLabelForErrorMessage>
        <div className={styles.formItemGroup}>
          <div className={styles.groupTitle}>项目信息</div>
          {
            ['edit', 'view'].includes(mode) && (<Grid.Row gutter="20">
              <Grid.Col span="12">
                <Form.Item
                  name="projectId"
                  label="项目id"
                  required={true}
                  labelCol={{ fixedSpan: 8 }}
                  wrapperCol={{ span: 14 }}
                  labelAlign="left"
                  colon
                  disabled={mode === 'edit'}
                  isPreview={mode === 'view'}
                >
                  <Input
                    className={styles.inputWidth}
                    placeholder="请输入"
                    onInput={(e: any) => {
                      // Remove any non-digit characters and limit to 20 digits
                      let value = e.target.value.replace(/\D/g, '');
                      if (value.length > 20) {
                        value = value.slice(0, 20);
                      }
                      e.target.value = value;
                    }}
                  />
                </Form.Item>
              </Grid.Col>
            </Grid.Row>)
          }
          <Grid.Row gutter="20">
            <Grid.Col span="12">
              <Form.Item
                name="enterpriseId"
                label="归属企业名称"
                required={true}
                labelCol={{ fixedSpan: 8 }}
                wrapperCol={{ span: 14 }}
                labelAlign="left"
                colon
                disabled={mode === 'edit'}
                isPreview={mode === 'view'}
              >
                <Select
                  followTrigger
                  className={styles.inputWidth}
                  placeholder="请选择"
                  dataSource={enterpriseOptions}
                  onChange={handleEnterpriseChange}
                ></Select>
              </Form.Item>
            </Grid.Col>
          </Grid.Row>
          <Grid.Row gutter="20">
            <Grid.Col span="12">
              <Form.Item
                name="projectName"
                label="项目名称"
                required={true}
                labelCol={{ fixedSpan: 8 }}
                wrapperCol={{ span: 14 }}
                labelAlign="left"
                colon
                isPreview={mode === 'view'}
              >
                <Input className={styles.inputWidth} placeholder="请输入" />
              </Form.Item>
            </Grid.Col>
          </Grid.Row>
        </div>
        <div className={styles.formItemGroup}>
          <div className={styles.groupTitle}>合同信息</div>
          <Grid.Row gutter="20">
            <Grid.Col span="24">
              <Form.Item
                name="contractId"
                label="合同平台合同id"
                required={true}
                labelCol={{ fixedSpan: 8 }}
                wrapperCol={{ span: 14 }}
                labelAlign="left"
                colon
                // 去掉 (mode === 'edit' && fields?.getValue('status') !== 'pending') 编辑模式也可以编辑
                isPreview={mode === 'view'}
                help={
                  mode === 'view' ? null :
                    <a
                      href=""
                      target="_blank"
                      style={{ fontSize: '12px' }}
                      onClick={async (e) => {
                        e.preventDefault();
                        const contractId = fields.getValue('contractId');
                        await handleContractQuery(String(contractId), fields);
                      }}
                    >
                      查询
                    </a>
                }
              >
                <Input
                  placeholder="请输入"
                  className={styles.inputWidth}
                  maxLength={20}
                  onInput={(e: any) => {
                    // Remove any non-digit characters and limit to 20 digits
                    let value = e.target.value.replace(/\D/g, '');
                    if (value.length > 20) {
                      value = value.slice(0, 20);
                    }
                    e.target.value = value;
                  }} />
              </Form.Item>
            </Grid.Col>
          </Grid.Row>
          <Grid.Row gutter="20">
            <Grid.Col span="24">
              <Form.Item
                name="contractName"
                label="名称"
                required={true}
                labelCol={{ fixedSpan: 8 }}
                wrapperCol={{ span: 14 }}
                labelAlign="left"
                colon
                isPreview={true}
                help={
                  mode === 'view' || !fields?.getValue('detailUrl') ? null :
                    <a href={fields?.getValue('detailUrl')} target="_blank" style={{ fontSize: '12px' }}>
                      查看合同
                    </a>
                }
              >
                <Input className={styles.inputWidth} placeholder="请输入" />
              </Form.Item>
            </Grid.Col>
          </Grid.Row>
          <Grid.Row gutter="20">
            <Grid.Col span="24">
              <Form.Item
                name="ourCompanyName"
                label="签约主体(我方)"
                required={true}
                labelCol={{ fixedSpan: 8 }}
                wrapperCol={{ span: 14 }}
                labelAlign="left"
                colon
                isPreview={true}
              >
                <Input className={styles.inputWidth} placeholder="请输入" />
              </Form.Item>
            </Grid.Col>
          </Grid.Row>
          <Grid.Row gutter="20">
            <Grid.Col span="24">
              <Form.Item
                name="otherCompanyName"
                label="签约主体(对方)"
                required={true}
                labelCol={{ fixedSpan: 8 }}
                wrapperCol={{ span: 14 }}
                labelAlign="left"
                colon
                isPreview={true}
              >
                <Input className={styles.inputWidth} placeholder="请输入" />
              </Form.Item>
            </Grid.Col>
          </Grid.Row>
          <Grid.Row gutter="20">
            <Grid.Col span="24">
              <Form.Item
                name="contractPeriod"
                label="合同有效期"
                required={true}
                labelCol={{ fixedSpan: 8 }}
                wrapperCol={{ span: 14 }}
                labelAlign="left"
                colon
                isPreview
              >
                <DatePicker2.RangePicker
                  className={styles.inputWidth}
                  placeholder={['请选择时间', '请选择时间']}
                  format="YYYY-MM-DD HH:mm:ss"
                  // @ts-ignore
                  showTime={{
                    defaultValue: ['00:00:00', '23:59:59'],
                  }}
                />
              </Form.Item>
            </Grid.Col>
          </Grid.Row>
          <Grid.Row gutter="20">
            <Grid.Col span="24">
              <Form.Item
                name="contractAmount"
                label="合同金额"
                required={true}
                labelCol={{ fixedSpan: 8 }}
                wrapperCol={{ span: 14 }}
                labelAlign="left"
                colon
                isPreview={true}
              >
                <NumberPicker className={styles.inputWidth} placeholder="请输入" innerAfter={fields?.getValue('contractAmount')? '元' : ''} min={0} />
              </Form.Item>
            </Grid.Col>
          </Grid.Row>
          <Grid.Row gutter="20">
            <Grid.Col span="24">
              <Form.Item
                name="creditAmount"
                label="滚动授信用金额"
                // 合同金额 为 0 时 滚动授信用金额 不必填
                required={Number(fields?.getValue('contractAmount')) === 0 ? false : true }
                labelCol={{ fixedSpan: 8 }}
                wrapperCol={{ span: 14 }}
                labelAlign="left"
                colon
                isPreview={mode === 'view'}
                validator={(rule, value) => {
                  const contractAmount = Number(fields?.getValue('contractAmount'));
                  const creditAmount = Number(value);
                  if ((value === null || value === undefined || value === '') && 
                      contractAmount === 0) {
                    return Promise.resolve();
                  }
                  if (creditAmount > contractAmount) {
                    return Promise.reject('滚动授信用金额不能大于合同金额');
                  }
                  return Promise.resolve();
                }}
              >
                <NumberPicker className={styles.inputWidth} placeholder="请输入" innerAfter="元" min={0} />
              </Form.Item>
            </Grid.Col>
          </Grid.Row>
          <Grid.Row gutter="20">
            <Grid.Col span="24">
              <Form.Item
                name="icServiceRate"
                label="技术服务费率"
                required={true}
                labelCol={{ fixedSpan: 8 }}
                wrapperCol={{ span: 14 }}
                labelAlign="left"
                colon
                // todo 编辑态前端不可编辑（上线前加，测试时允许修改）
                isPreview={mode === 'view'}
              >
                <Select className={styles.inputWidth} dataSource={[{ label: '0%', value: '0' }, { label: '3%', value: '3' }]} />
              </Form.Item>
            </Grid.Col>
          </Grid.Row>
        </div>
        <div className={styles.formItemGroup}>
          <div className={styles.groupTitle}>结算信息</div>
          <div className={styles.gridContainer}>
            <Grid.Row gutter="10">
              <Grid.Col span="24">
                <Form.Item
                  name="orderConfirmPeriod"
                  label="账单汇总周期"
                  required={true}
                  labelCol={{ fixedSpan: 8 }}
                  wrapperCol={{ span: 14 }}
                  labelAlign="left"
                  colon
                  isPreview={mode === 'view'}
                >
                  <div style={{ display: 'flex', gap: '5px', alignItems: 'center' }}>
                    <span className={styles.textStyle}>每</span>
                    <NumberPicker
                      {...fields.init('intervalCycle', {
                        initValue: 1,
                        props: {
                          onChange: handleIntervalCycleChange
                        }
                      })}
                      min={1}
                      max={12}
                      innerAfter="个月"
                      isPreview={mode === 'view'}
                    />
                    <NumberPicker
                      {...fields.init('intervalDate', {
                        initValue: 10,
                      })}
                      min={1}
                      max={27}
                      innerAfter="号"
                      isPreview={mode === 'view'}
                    />
                    <span style={{ color: '#999', fontSize: '12px', marginLeft: '8px' }}>
                      备注：和企业客户产生的所有月份内月初到月底的服务费用
                    </span>
                  </div>
                </Form.Item>
              </Grid.Col>
            </Grid.Row>
            <Grid.Row gutter="10">
              <Grid.Col span="24">
                <Form.Item
                  name="settlementPeriod"
                  label="财务结算周期"
                  required={true}
                  labelCol={{ fixedSpan: 8 }}
                  wrapperCol={{ span: 24 }}
                  labelAlign="left"
                  colon
                  isPreview={mode === 'view'}
                >
                  <div style={{ display: 'flex', gap: '5px', alignItems: 'center' }}>
                    <span>周期单位</span>
                    <Select
                      {...fields.init('intervalType_settle', { initValue: 'month' })}
                      placeholder="请选择"
                      dataSource={[{ label: '月', value: 'month' }, { label: '日', value: 'day' }]}
                      onChange={(val) => {
                        fields.setValue('intervalType_settle', val);
                        // Reset interval cycle if it exceeds new max value
                        const currentValue = fields.getValue('intervalCycle_settle') as number;
                        const newMax = val === 'month' ? 12 : 365;
                        if (currentValue > newMax) {
                          fields.setValue('intervalCycle_settle', newMax);
                        }
                      }}
                      isPreview={mode === 'view'}
                    />
                    <span>周期数值</span>
                    <NumberPicker
                      {...fields.init('intervalCycle_settle', { initValue: 1 })}
                      defaultValue={1}
                      min={1}
                      max={fields.getValue('intervalType_settle') === 'month' ? 12 : 365}
                      onChange={(val) => {
                        fields.setValue('intervalCycle_settle', val);
                      }}
                      isPreview={mode === 'view'}
                    />
                    <span style={{ color: '#999', fontSize: '12px', marginLeft: '8px' }}>
                      备注：企业客户出账后多长时间内要完成付款，月代表自然月
                    </span>
                  </div>
                </Form.Item>
              </Grid.Col>
            </Grid.Row>
          </div>
        </div>
        <div className={styles.formItemGroup}>
          <div className={styles.groupTitle}>付款信息</div>
          <div className={styles.gridContainer}>
            <Grid.Row gutter="10">
              <Grid.Col>
                <Form.Item
                  name="invoiceSubjectList"
                  label="付款&开票主体"
                  required={true}
                  labelCol={{ fixedSpan: 8 }}
                  wrapperCol={{ span: 14 }}
                  labelAlign="left"
                  colon
                  isPreview={mode === 'view'}
                >
                  <Select
                    className={styles.inputWidth}
                    placeholder="请输入"
                    dataSource={enterpriseExtendOptions}
                    multiple
                  />
                </Form.Item>
              </Grid.Col>
            </Grid.Row>
          </div>
        </div>
        <div className={styles.formItemGroup}>
          <div className={styles.groupTitle}>项目联系人</div>
          <div className={styles.gridContainer}>
            <Form.Item
              name="name"
              label="姓名"
              required={true}
              labelCol={{ fixedSpan: 8 }}
              wrapperCol={{ span: 14 }}
              labelAlign="left"
              colon
              isPreview={mode === 'view'}
            >
              <Input className={styles.inputWidth} placeholder="请输入" maxLength={100} />
            </Form.Item>
            <Form.Item
              name="phoneNumber"
              label="手机号"
              required={true}
              labelCol={{ fixedSpan: 8 }}
              wrapperCol={{ span: 14 }}
              labelAlign="left"
              colon
              isPreview={mode === 'view'}
              validator={(rule, value) => validatePhoneNumber(value as string, true)}
            >
              <Input className={styles.inputWidth} placeholder="请输入" maxLength={11} />
            </Form.Item>
            <Form.Item
              name="email"
              label="邮箱"
              labelCol={{ fixedSpan: 8 }}
              wrapperCol={{ span: 14 }}
              labelAlign="left"
              colon
              isPreview={mode === 'view'}
              validator={(rule, value) => validateEmail(value as string)}
            >
              <Input className={styles.inputWidth} placeholder="请输入" />
            </Form.Item>
            <Form.Item
              name="address"
              label="联系地址"
              labelCol={{ fixedSpan: 8 }}
              wrapperCol={{ span: 14 }}
              labelAlign="left"
              colon
              isPreview={mode === 'view'}
            >
              <Input className={styles.inputWidth} placeholder="请输入" />
            </Form.Item>
          </div>
        </div>
      </Form>
    );
  };

  const renderOperationButtons = () => {
    const operationList = fields.getValue('operationList') || [];
    const operationHandlers = {
      'save': handleSave,
      'submit': handleSubmitAudit,
      'cancel': handleCancelAudit,
      'audit': () => setReviewModalVisible(true)
    };
    
    if (mode === 'add') {
      return (
        <div className={styles.formSubmitButtonContainer}>
          {['save', 'submit']?.map((operation: string) => {
            return (
              <OperationButton
                key={operation}
                operation={operation}
                onClick={operationHandlers[operation]}
              />
            );
          })}
        </div>
      );
    }

    return (
      <div className={styles.formSubmitButtonContainer}>
        {operationList.map((_operation: string) => {
          const operation = _operation.toLowerCase();
          return (
            <OperationButton
              key={operation}
              operation={operation}
              onClick={operationHandlers[operation]}
            />
          );
        })}
      </div>
    );
  };

  return (
    <>
      <Drawer 
        title={drawerTitle} 
        visible={visible && (!projectId || (!isLoading && isDataLoaded))}
        onClose={() => {
          onClose?.();
          fields.resetToDefault();
        }} 
        width={1000}
      >
        <div className={styles.subProjectManage}>{renderMyForm()}</div>
        {renderOperationButtons()}
      </Drawer>
      <ReviewModal
        visible={reviewModalVisible}
        onClose={() => setReviewModalVisible(false)}
        onApprove={handleApprove}
        onReject={handleReject}
        modalTitle="项目审核"
      />
    </>
  );
};

export default ProjectManageDrawer;
