import React from 'react';
import { Button, Message } from '@alifd/next';
import { isTrue } from '@/pages/Utils';
import styles from '@/pages/ProjectManage/index.module.scss';

const ProjectActions = ({ value, index, record, onEditProject, onViewProject, onDeleteProject, onInvalidProject, handleAddProjectItemsPool }) => {
  const operationList = record?.operationList || [];

  return (
    <div className={styles.actions}>
      {operationList.includes('EDIT') && (
        <Button
          type="primary"
          text
          onClick={() => onEditProject(record)}
          style={{ marginRight: '5px' }}
        >
          编辑
        </Button>
      )}
      {operationList.includes('VIEW') && (
        <Button
          type="primary"
          text
          onClick={() => onViewProject(record)}
          style={{ marginRight: '5px' }}
        >
          查看
        </Button>
      )}
      {operationList.includes('DELETE') && (
        <Button
          type="primary"
          text
          onClick={() => onDeleteProject(record)}
          style={{ marginRight: '5px' }}
        >
          删除
        </Button>
      )}
      {operationList.includes('INVALIDATE') && (
        <Button
          type="primary"
          text
          onClick={() => onInvalidProject(record)}
          style={{ marginRight: '5px' }}
        >
          失效
        </Button>
      )}
      {operationList.includes('ADMIN_ITEM_POOL') && (
        <Button
          type="primary"
          text
          onClick={async () => {
            if (record?.poolId) {
              window.open(`/biz-purchase/product-list-manage?poolId=${record?.poolId}`, '_blank');
            }
          }}
          style={{ marginRight: '5px' }}
        >
          管理商品池
        </Button>
      )}
       {operationList.includes('ADD_ITEM_POOL') && (
        <Button
          type="primary"
          text
          onClick={() => handleAddProjectItemsPool(record)}
          style={{ marginRight: '5px' }}
        >
          新增商品池
        </Button>
      )}
    </div>
  );
};

export default ProjectActions; 