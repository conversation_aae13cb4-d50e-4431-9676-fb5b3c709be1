import React from 'react';
import { Pagination } from '@alifd/next';
import styles from '@/pages/ProjectManage/index.module.scss';

const ProjectPagination = ({ pagination, onChange }) => {
  return (
    <div>
      <Pagination
        {...pagination}
        onChange={(current, pageSize) => onChange(current, pagination?.pageSize)}
        onPageSizeChange={(size) => onChange(1, size)}
        className={styles.myPagination}
        type="normal"
        shape="normal"
        showJump
        pageSizeSelector="dropdown"
        pageSizeList={[10, 20]}
        totalRender={(total) => <span className={styles.totalText}> 共 {total} 条</span>}
      />
    </div>
  );
};

export default ProjectPagination; 