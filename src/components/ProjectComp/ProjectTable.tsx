import React from 'react';
import AdaptiveTable from '@/components/AdaptiveTable/index';
import { Table, Button } from '@alifd/next';
import StatusTag from '@/components/EnterpriseManageComp/StatusTag';
import ProjectActions from './ProjectActions';
import ListStatusTag from '@/components/ListStatusTag';
import styles from '@/pages/ProjectManage/index.module.scss';
import { formatTimestamp } from '@/pages/Utils';


const ProjectTable = ({ dataSource, loading, onNewProject, onEditProject, onViewProject, onDeleteProject, onInvalidProject, handleAddProjectItemsPool }) => {
  return (
    <div className={styles.cardEnterpriseList}>
      <div className={styles.cardTitleContainer}>
        <span className={styles.cardTitle}>项目列表</span>
        <Button
          className={styles.cardEnterpriseListButton}
          type="primary"
          onClick={onNewProject}
        >
          新增项目
        </Button>
      </div>
      <div className={styles.listContainer}>
        <AdaptiveTable dataSource={dataSource} primaryKey="my_id" loading={loading} adaptiveThreshold={1700} emptyContent={<div> 暂无数据 </div>}>
          <Table.Column title="项目Id" dataIndex="projectId" width={100} lock="left" />
          <Table.Column title="项目名称" dataIndex="projectName" width={200} lock="left" />
          <Table.Column title="企业名称" dataIndex="enterpriseDTO.enterpriseName" width={200} />
          <Table.Column title="商品池商品数量" dataIndex="poolItemNum" width={120} />
          <Table.Column title="可付款主体数" dataIndex="subjectNum" width={120} />
          <Table.Column title="授信额度" dataIndex="creditAmount" width={120} />
          <Table.Column title="创建时间" dataIndex="gmtCreate" cell={formatTimestamp} width={160} />
          <Table.Column title="最近编辑时间" dataIndex="gmtModified" cell={formatTimestamp} width={160} />
          <Table.Column title="最近一次编辑者" dataIndex="updateOperator" width={120} />
          <Table.Column title="项目状态" dataIndex="status" cell={(value) => <ListStatusTag status={value?.toUpperCase() } />} width={100} lock="right" />
          <Table.Column title="审核状态" dataIndex="auditStatus" cell={(value, index, record) => <StatusTag value={value?.toUpperCase() } record={record} />} width={100} lock="right" />
          <Table.Column title="操作" cell={(value, index, record) => (
            <ProjectActions 
              value={value}
              index={index}
              record={record}
              onEditProject={onEditProject}
              onViewProject={onViewProject}
              onDeleteProject={onDeleteProject}
              onInvalidProject={onInvalidProject}
              handleAddProjectItemsPool={handleAddProjectItemsPool}
            />
          )} width={200} lock="right" />
        </AdaptiveTable>
      </div>
    </div>
  );
};

export default ProjectTable; 