import React, { useState } from 'react';
import { Input, Select, DatePicker2, Button, Grid, Form, Field, NumberPicker } from '@alifd/next';
import CategoryTreeSelect from '@/components/CategoryTreeSelect';
import BrandSelect from '@/components/BrandSelect';
import { applyStatusOptions } from '@/pages/Utils';

import styles from './index.module.scss';

interface SearchFormProps {
  onSearch: () => void;
  onReset: () => void;
  ref?: React.Ref<any>;
}
// @ts-ignore
const SearchForm: React.FC<SearchFormProps> = React.forwardRef(({ onSearch, onReset }, ref) => {
  const fields = Field.useField({});

  // 暴露方法给外部组件
  React.useImperativeHandle(ref, () => ({
    getValues: () => {
      const values = fields.getValues();
      // 处理创建时间范围
      if (values.creationTime) {
        values.gmtCreateStart = values.creationTime[0].valueOf();
        values.gmtCreateEnd = values.creationTime[1].valueOf();
        delete values.creationTime;
      }
      if (values.leafCategoryData) {
        values.leafCategoryName = values.leafCategoryData;
        delete values.leafCategoryData;
      }
      if (values.brandNameString) {
        values.brandName = values.brandNameString;
        delete values.brandNameString;
      }
      if(values.itemId){
        values.itemId = String(values.itemId);
      }
      return values;
    },
    resetFields: () => fields.resetToDefault(),
  }));

  const formItemLayout = {
    labelCol: {
      fixedSpan: 8,
    },
    wrapperCol: {
      span: 16,
    },
  };

  return (
    <Form className={styles.searchSection} field={fields} labelAlign="top" {...formItemLayout}>
      <Grid.Row gutter="10">
        <Grid.Col span={4}>
          <Form.Item name="title" label="商品名称">
            <Input placeholder="请输入商品名称" />
          </Form.Item>
        </Grid.Col>
        <Grid.Col span={4}>
          <Form.Item name="itemId" label="商品ID">
            <NumberPicker placeholder="请输入商品ID" style={{ width: '100%' }} hasTrigger={false} />
          </Form.Item>
        </Grid.Col>
        <Grid.Col span={4}>
          <Form.Item name="applyStatus" label="报名状态">
            <Select placeholder="请选择" dataSource={applyStatusOptions} style={{ width: '100%' }} hasClear followTrigger/>
          </Form.Item>
        </Grid.Col>
        <Grid.Col span={4}>
          <Form.Item name="leafCategoryName" label="叶子类目名称">
            <CategoryTreeSelect
              onChange={(value, option) => {
                fields.setValue('leafCategoryData', value);
              }}
            />
          </Form.Item>
        </Grid.Col>
        <Grid.Col span={4}>
          <Form.Item name="brandName" label="品牌">
            <BrandSelect onChange={(value) => {
              fields.setValue('brandNameString', value);
            }}/>
          </Form.Item>
        </Grid.Col>
        <Grid.Col span={4}>
          <Form.Item name="sellerName" label="商家名称">
            <Input placeholder="请输入商家名称" style={{ width: '100%' }} />
          </Form.Item>
        </Grid.Col>
      </Grid.Row>
      <Grid.Row gutter="10">
        <Grid.Col span={8}>
          <Form.Item name="creationTime" label="创建时间">
            <DatePicker2.RangePicker
              className={styles.rangePicker}
              followTrigger
              placeholder={['请选择时间', '请选择时间']}
              style={{ width: '100%' }}
              format="YYYY-MM-DD HH:mm:ss"
              // @ts-ignore
              showTime={{
                defaultValue: ['00:00:00', '23:59:59'],
              }}
            />
          </Form.Item>
        </Grid.Col>
        <Grid.Col span={4}>
          <Form.Item label={' '}>
            <div className={styles.rcodeSearchBtns}>
              <Button
                className={styles.searchBtnsButton}
                type="primary"
                onClick={() => {
                  onSearch();
                }}
              >
                查询
              </Button>
              <Button
                className={styles.searchBtnsButton}
                type="normal"
                onClick={() => {
                  fields.resetToDefault();
                  onReset();
                }}
              >
                重置
              </Button>
            </div>
          </Form.Item>
        </Grid.Col>
      </Grid.Row>
    </Form>
  );
});

export default SearchForm;
