import React, { useEffect, useState } from "react";
import { Dialog, Message, Radio, Input } from "@alifd/next";

import { auditItem } from "@/services";
import { IAuditItemParams } from "@/types";
import { isTrue } from "@/pages/Utils";

const AuditDialog = ({ visible, onClose, currentItemData, selectedRows, handleSearch }) => {
    const [auditStatus, setAuditStatus] = useState<IAuditItemParams['auditType']>('approve');
    const [rejectReason, setRejectReason] = useState('');
  
    const handleAudit = () => {
      if (auditStatus === 'reject' && !rejectReason) {
        Message.error('请输入拒绝原因');
        return;
      }
      // 批量审核或者单个审核
      const targetIdList = selectedRows?.length
        ? selectedRows.map((item) => item?.itemId?.toString())
        : [currentItemData?.itemId?.toString()];
      auditItem({
        targetType: 'item',
        targetIdList: targetIdList,
        auditType: auditStatus,
        mark: auditStatus === 'reject' ? rejectReason : '',
      }).then((res) => {
        if (isTrue(res?.success) && res?.data?.successCount === targetIdList?.length) {
          Message.success('审核成功');
          setTimeout(() => {
            handleSearch?.();
            onClose();
          }, 1000);
        }else if(res?.data?.successCount || res?.data?.failCount) {
          Dialog.warning({
            title: '审核结果',
            content: `通过：${res?.data?.successCount || 0} 条，失败：${res?.data?.failCount || 0} 条`,
            onOk: () => {
              onClose();
            },
          });
        } else {
          // 全部失败
          Message.error(res?.message || '审核失败');
        } 
      });
    };

    useEffect(() => {
      if (visible) {
        setAuditStatus('approve');
        setRejectReason('');
      }
    }, [visible]);

    return (
      <Dialog
        visible={visible}
        onClose={onClose}
        title="审核"
        onOk={handleAudit}
        onCancel={onClose}
        style={{ width: '400px' }}
      >
        <Radio.Group value={auditStatus} onChange={(value) => setAuditStatus(value as IAuditItemParams['auditType'])}>
          <Radio value="approve">通过</Radio>
          <Radio value="reject">拒绝</Radio>
        </Radio.Group>
        {auditStatus === 'reject' && (
          <Input.TextArea
            style={{ marginTop: '10px', width: '100%' }}
            value={rejectReason}
            onChange={(value) => setRejectReason(value)}
            placeholder="请输入拒绝原因"
            maxLength={200}
          />
        )}
      </Dialog>
    );
};

export default AuditDialog;