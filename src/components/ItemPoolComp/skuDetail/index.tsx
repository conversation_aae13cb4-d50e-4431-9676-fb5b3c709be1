import React, { useState, useEffect } from 'react';
import { Table, Dialog, Switch, NumberPicker, Message, Form, Input } from '@alifd/next';
import { editSku } from '@/services';

import { Product, ProductItem, IEditSkuParams } from '@/types';
import styles from './index.module.scss';
import { isTrue } from '@/pages/Utils';

//  poolType !== 'add' 标识非批量新增商品池页面

const SkuDetail = ({
  mode,
  visible,
  onClose,
  poolType,
  itemId,
  currentItemData,
  onUpdate,
}: {
  mode: string;
  poolType?: string;
  visible: boolean;
  onClose: () => void;
  itemId?: number | string | null;
  currentItemData: Product & { poolId: number, platformPrice: string, price: string };
  onUpdate?: (data: ProductItem) => void;
}) => {
  const [editTableDataSource, setEditTableDataSource] = useState(currentItemData?.skuList);

  useEffect(() => {
    if (currentItemData?.skuList?.length > 0 && visible) {
      setEditTableDataSource(currentItemData.skuList);
    } else {
      setEditTableDataSource([]);
    }
  }, [itemId, currentItemData?.skuList?.length, visible]);

  const hasNoEnabledSku = () => {
    if (!editTableDataSource?.length) {
      return false;
    }
    
    const hasEnabled = editTableDataSource.filter(item => 
      item?.enable || item?.enable === undefined
    ).length === 0;

    if (hasEnabled && mode === 'edit') {
      Dialog.warning({
        title: '至少启用一个sku',
      });
    }
    return hasEnabled;
  };

  const handleClose = () => {
    if (poolType !== 'add' && mode === 'edit' && hasNoEnabledSku()) {
      return false;
    }
    onClose?.();
  };

  const handleSubmit = () => {
    if (hasNoEnabledSku()) {
      return;
    }
    
    const newCurrentItemData = {
      ...currentItemData,
      skuList: editTableDataSource,
    };
    
    if (poolType === 'add') {
      editSku({
        poolId: currentItemData?.poolId,
        itemId: currentItemData?.itemId,
        ...(editTableDataSource?.length ? {} : { ratioPrice: 0 }),
        skuList: editTableDataSource.map((item) => ({
          skuId: item?.skuId,
          ratioPrice: item?.ratioPrice || 0,
          enable: item?.enable ?? true,
        })),
      } as IEditSkuParams).then((res) => {
        if (isTrue(res.success)) {
          Message.success('编辑成功');
          onUpdate?.(newCurrentItemData);
          onClose?.();
        } else {
          Message.error(res?.message || '编辑失败');
        }
      });
    } else {
      onClose?.();
    }
  };

  // 渲染已入池的sku编辑时候 sku 数量为空单独显示商品编辑
  const renderProductInfo = () => {
    const formItemLayout = {
      labelCol: {
        fixedSpan: 4,
      },
      wrapperCol: {
        span: 16,
      },
    };
    return <Form labelAlign='left' colon {...formItemLayout}>
      <Form.Item label="市场价" required >
        <Input value={currentItemData?.price} isPreview/>
      </Form.Item>
      <Form.Item label="批发价" required>
        <Input value={currentItemData?.egoPrice} isPreview/>
      </Form.Item>
      <Form.Item label="加价比例" required>
        <NumberPicker defaultValue={0} min={0} max={100} precision={2} format={(val) => `${val}%`} disabled/>
      </Form.Item>
      <Form.Item label="专属价" required>
        <Input value={currentItemData?.platformPrice} required isPreview/>
      </Form.Item>
    </Form>
  }

  const renderMyDialog = () => {
    const isEmptySkuListInAddMode = poolType === 'add' && !editTableDataSource?.length;
    const dialogWidth = isEmptySkuListInAddMode ? '580px' : '800px';
    const dialogTitle = mode === 'view' 
      ? '查看' 
      : poolType === 'pending' ? '选择sku' : '编辑';
      
    return (
      <Dialog
        v2
        title={dialogTitle}
        footerAlign="right"
        visible={visible}
        footer={mode === 'view' ? false : undefined}
        className={styles.rcodeDialog}
        style={{ width: dialogWidth }}
        onOk={handleSubmit}
        onCancel={handleClose}
        onClose={handleClose}
      >
        {/* Product info section */}
        {poolType === 'add' && (
          <div className={styles.productInfo} style={isEmptySkuListInAddMode ? { 
            marginBottom: 0,
            borderBottom: 'none'
          } : {}}>
            <div className={styles.productImage}>
              <img src={currentItemData?.imageUrl} alt="商品图片" />
            </div>
            <div className={styles.productDetails}>
              <a
                href={`https://item.taobao.com/item.htm?id=${currentItemData?.itemId}`}
                target="_blank"
                rel="noopener noreferrer"
                className={styles.productTitle}
              >
                {currentItemData?.title}
              </a>
              <div className={styles.productId}>商品ID: {currentItemData?.itemId}</div>
            </div>
          </div>
        )}

        {/* Render product info form or SKU table */}
        {isEmptySkuListInAddMode ? (
          renderProductInfo()
        ) : (
          <>
            <div className={styles.skuListTitle}>sku列表({editTableDataSource?.length})</div>
            <div className={styles.editTableContainer}>
              <div style={{ margin: '20px 0' }}>
                <Table dataSource={editTableDataSource} emptyContent={editTableDataSource?.length === 0 ? '暂无sku' : ''}>
                  <Table.Column lock="left" width={130} title="skuId" dataIndex="skuId" />
                  <Table.Column
                    width={100}
                    title="图片"
                    dataIndex="skuImageUrl"
                    cell={(value, index, record) => {
                      return <img src={currentItemData?.imageUrl} alt="sku" style={{ width: '50px', height: '50px' }} />;
                    }}
                  />
                  <Table.Column
                    width={100}
                    title="规格"
                    dataIndex="prop"
                    cell={(value, index, record) => {
                      return <div className={styles.skuProp} title={record?.prop}>{record?.prop}</div>;
                    }}
                  />
                  <Table.Column
                    width={100}
                    title="普惠券后价"
                    dataIndex="referencePrice"
                    cell={(value, index, record) => {
                      return record?.egoReferencePrice;
                    }}
                  />
                  <Table.Column width={120} title="市场价(一口价)" dataIndex="skuPrice" />
                  <Table.Column
                    width={100}
                    title="批发价"
                    dataIndex="egoPrice"
                    cell={(value, index, record) => {
                      return record?.egoPrice;
                    }}
                  />
                  {mode === 'edit' && poolType !== 'pending' && (
                    <Table.Column
                      width={120}
                      title="加价比例"
                      dataIndex="ratioPrice"
                      cell={(value, index, record) => {
                        return (
                          <NumberPicker
                            disabled={true}
                            min={0}
                            max={100}
                            value={value || 0}
                            defaultValue={0}
                            onChange={(val) => {
                              const newData = [...editTableDataSource];
                              newData[index].ratioPrice = val !== undefined ? val.toString() : '0';
                              setEditTableDataSource(newData);
                            }}
                            format={(val) => `${val}%`}
                          />
                        );
                      }}
                    />
                  )}
                  {poolType === 'add' && <Table.Column width={120} title="专属价" dataIndex="platformPrice" />}
                  {mode === 'edit' && (
                    <Table.Column
                      width={100}
                      title="启用"
                      dataIndex="enable"
                      lock="right"
                      cell={(value, index, record) => {
                        return (
                          <Switch
                            checked={record?.enable !== undefined ? record?.enable : true}
                            onChange={(val) => {
                              const newData = [...editTableDataSource];
                              newData[index].enable = val;
                              setEditTableDataSource(newData);
                            }}
                          />
                        );
                      }}
                    />
                  )}
                </Table>
              </div>
            </div>
            {mode === 'edit' && (
              <div className={styles.selectedSkuTitle}>
                已选sku({editTableDataSource?.filter((item) => item.enable || item.enable === undefined)?.length || 0})
              </div>
            )}
          </>
        )}
      </Dialog>
    );
  };

  return <div className={styles.skuEdit}>{renderMyDialog()}</div>;
};

export default SkuDetail;
