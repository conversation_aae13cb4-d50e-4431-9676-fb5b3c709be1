.rcodeDialog {
  :global {
    .next-dialog-header {
      border-bottom: 1px solid #ccc;
    }
  }

  .editTableAddAction {
    margin: 12px 0px;
    border: 1px dashed #dee0e5;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    color: var(--color-brand1-6, #4f89ff);
    text-align: center;
    padding: 7px 0;
  }
  .editTableContainer {
    .editTableTitle {
      color: #111;
      font-size: 18px;
      font-weight: 500;
      line-height: 26px;
    }
  }
  .skuListTitle {
    color: #000000;
    font-size: 18px;
    font-weight: bold;
  }

  .selectedSkuTitle {
    color: #000000;
    font-size: 18px;
    font-weight: bold;
  }

  .skuProp {
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100px;
    // 最多四行
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
  }
}

.productInfo {
  display: flex;
  padding: 16px;
  margin-bottom: 10px;
  margin-top: -18px;
  border-bottom: 1px solid #e8e8e8;
  
  .productImage {
    width: 100px;
    height: 100px;
    margin-right: 16px;
    
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  
  .productDetails {
    flex: 1;
    
    .productTitle {
      display: block;
      font-size: 14px;
      color: #333;
      margin-bottom: 8px;
      text-decoration: none;
      
      &:hover {
        color: #1890ff;
      }
    }
    
    .productId {
      color: #666;
      font-size: 12px;
    }
  }
}
