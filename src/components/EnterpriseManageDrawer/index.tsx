import React, { useState, useEffect } from 'react';
import { Form, Field, Drawer, Message, Dialog } from '@alifd/next';
import SubEnterpriseDialog from './subEnterpriseDialog';
import { queryEnterpriseDetail, addEnterprise, submitEnterpriseAudit, auditAll } from '@/services';
import ReviewModal from '@/components/ReviewModal';
import EnterpriseInfoForm from './EnterpriseInfoForm'
import SubEnterpriseList from './SubEnterpriseList';
import ActionButtons from './ActionButtons';
import StatusTag from '@/components/EnterpriseManageComp/StatusTag';
import ListStatusTag from '@/components/ListStatusTag';

import { isTrue } from '@/pages/Utils';
import { SubEnterpriseItem } from '@/types';

import styles from './index.module.scss';

interface SubEnterpriseManageProps {
  visible: boolean;
  onClose: () => void;
  roleId?: number;
  onRefresh: () => void;
  mode: 'edit' | 'add' | 'view';
}

const titleMap = { 'edit': '编辑企业', 'view': '查看企业', 'add': '新增企业' };

const SubEnterpriseManage: React.FC<SubEnterpriseManageProps> = ({ visible, onClose, roleId, mode, onRefresh }) => {
  const [subEnterpriseDialogVisible, setSubEnterpriseDialogVisible] = useState(false);
  const [subEnterpriseDialogMode, setSubEnterpriseDialogMode] = useState<'add' | 'edit'>('add');
  const [subEnterpriseDialogEditIndex, setSubEnterpriseDialogEditIndex] = useState<number>(0);
  const [editTableDataSource, setEditTableDataSource] = useState<SubEnterpriseItem[]>([]);
  const fields = Field.useField();
  const [auditDialogVisible, setAuditDialogVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isDataLoaded, setIsDataLoaded] = useState(false);

  useEffect(() => {
    fields.reset();
    setEditTableDataSource([]);
    if ((mode === 'edit' || mode === 'view') && visible) {
      setIsLoading(true);
      queryEnterpriseDetail({ roleId: roleId }).then((_data) => {
        const data = _data?.data;
        if (data) {
          const picUrl = data?.properties?.businessLicenseUrl;
          const picData = {
            uid: '0',
            name: 'IMG.png',
            state: 'done',
            url: picUrl,
            downloadURL: picUrl,
            imgURL: picUrl,
          };
          fields.setValues({
            operationList: data?.operationList || [],
            status: data?.status ?? '',
            auditStatus: data?.auditStatus ?? '',
            auditUrl: data?.auditUrl ?? '',
            roleId: data?.roleId ?? '',
            editVersion: data?.editVersion ?? '',
            name: data?.name ?? '',
            outId: data?.outId ?? '',
            businessLicenseUrl: [picData] ?? [],
            telephoneNumber: data?.properties?.telephoneNumber ?? '',
            address: data?.properties?.address ?? '',
            bankName: data?.properties?.bankName ?? '',
            bankAccount: data?.properties?.bankAccount ?? '',
            invoiceAddress: data?.properties?.invoiceAddress ?? '',
            invoiceEmail: data?.properties?.invoiceEmail ?? '',
            invoicePerson: data?.properties?.invoicePerson ?? '',
            invoiceTelephoneNumber: data?.properties?.invoiceTelephoneNumber ?? '',
            bankCardNo: data?.properties?.bankCardNo ?? '',
            branchNo: data?.properties?.branchNo ?? '',
            bankCertName: data?.properties?.bankCertName ?? '',
            branchName: data?.properties?.branchName ?? '',
          });
          setEditTableDataSource(data?.subList?.map((item) => ({ ...item, mainEnterpriseName: data?.name })) || []);
          data && setIsDataLoaded(true);
        }
      }).finally(() => {
        setIsLoading(false);
      });
    }
    setIsDataLoaded(mode === 'add');
  }, [roleId, visible, fields, mode]);

  const handleView = (index) => {
    setSubEnterpriseDialogVisible(true);
    setSubEnterpriseDialogMode('view');
    setSubEnterpriseDialogEditIndex(index);
  }

  const handleEdit = (index) => {
    setSubEnterpriseDialogVisible(true);
    setSubEnterpriseDialogMode('edit');
    setSubEnterpriseDialogEditIndex(index);
  };

  const handleAdd = () => {
    setSubEnterpriseDialogMode('add');
    setSubEnterpriseDialogVisible(true);
  };

  const handleDelete = (index) => {
    const newData = editTableDataSource.filter((item, idx) => idx !== index);
    setEditTableDataSource(newData);
  };

  const handleDialogClose = () => {
    setSubEnterpriseDialogVisible(false);
  };

  const handleSubmit = async () => {
    try {
      // First validate the form
      const result = await fields.validatePromise() as any;
      if (result?.errors) {
        return;
      }

      // Pass false to indicate this is part of submit flow (don't show save success message)
      const saveResponse = await handleSaveForm({ skipSuccessMessage: true });

      const formData = fields.getValues();
      let roleId = formData?.roleId;

      if (saveResponse?.data?.roleId) {
        roleId = saveResponse?.data?.roleId;
      }

      if (!roleId) {
        Message.error('获取企业ID失败');
        return;
      }

      // Query the latest enterprise information
      const enterpriseDetail = await queryEnterpriseDetail({ roleId: Number(roleId) });
      const data = enterpriseDetail?.data;

      if (!data) {
        Message.error('获取企业信息失败');
        return;
      }

      // Update form with latest data
      fields.setValues({
        roleId: data?.roleId,
        editVersion: data?.editVersion,
      });

      // Submit for audit with updated information
      const response = await submitEnterpriseAudit({
        roleId: Number(data?.roleId),
        editVersion: Number(data?.editVersion),
      });

      if (isTrue(response?.success)) {
        Dialog.success({
          title: "提示",
          content: <div>
            提交审核成功，
            <a href={response?.data?.auditUrl} target="_blank" rel="noopener noreferrer">
              查看审批流
            </a>
          </div>
        });
        onClose();
        setTimeout(() => {
          onRefresh();
        }, 1000);
      } else {
        Message.error(response?.message || '提交审核失败');
      }
    } catch (error) {
      Message.error(error?.message || '提交审核失败');
    }
  };

  const handleSaveForm = async ({ skipSuccessMessage = false, validateOnlyRequired = false }: { skipSuccessMessage?: boolean, validateOnlyRequired?: boolean }, customSubList?: any[]) => {
    const result = validateOnlyRequired ? await fields.validatePromise(['name', 'outId']) : await fields.validatePromise();
    if (result?.errors) {
      return Promise.reject(result.errors);
    }
    const formData = fields.getValues();
    const subListToUse = customSubList || editTableDataSource;
    const saveParams = {
      editVersion: 0,
      outId: formData?.outId,
      name: formData?.name,
      properties: {
        businessLicenseUrl: formData?.businessLicenseUrl?.[0]?.url ?? '',
        telephoneNumber: formData?.telephoneNumber,
        address: formData?.address,
        bankName: formData?.bankName,
        bankAccount: formData?.bankAccount,
        invoiceAddress: formData?.invoiceAddress,
        invoiceEmail: formData?.invoiceEmail,
        invoicePerson: formData?.invoicePerson,
        invoiceTelephoneNumber: formData?.invoiceTelephoneNumber,
      },
      subList: (subListToUse || []).map((item) => ({
        ...(item?.outId ? { outId: item.outId } : {}),
        ...(item?.name ? { name: item.name } : {}),
        ...(item?.roleId ? { roleId: item.roleId } : {}),
        ...(item?.editVersion ? { editVersion: item.editVersion } : { editVersion: 0 }),
        properties: {
          businessLicenseUrl: Array.isArray(item?.properties?.businessLicenseUrl) ? item?.properties?.businessLicenseUrl?.[0]?.url : item?.properties?.businessLicenseUrl,
          telephoneNumber: item?.telephoneNumber || '',
          address: item?.address,
          bankName: item?.bankName,
          bankAccount: item?.bankAccount,
          invoiceAddress: item?.invoiceAddress,
          invoiceEmail: item?.invoiceEmail,
          invoicePerson: item?.invoicePerson,
          invoiceTelephoneNumber: item?.invoiceTelephoneNumber,
          // 收款账户信息不回传给后端接口
        }
      }))
    };

    const editParams = {
      roleId: formData?.roleId,
      editVersion: formData?.editVersion,
      outId: formData?.outId,
      name: formData?.name,
      properties: {
        businessLicenseUrl: formData?.businessLicenseUrl?.[0]?.url ?? '',
        telephoneNumber: formData?.telephoneNumber,
        address: formData?.address,
        bankName: formData?.bankName,
        bankAccount: formData?.bankAccount,
        invoiceAddress: formData?.invoiceAddress,
        invoiceEmail: formData?.invoiceEmail,
        invoicePerson: formData?.invoicePerson,
        invoiceTelephoneNumber: formData?.invoiceTelephoneNumber,
      },
      subList: (subListToUse || []).map((item) => ({
        ...(item?.outId ? { outId: item.outId } : {}),
        ...(item?.name ? { name: item.name } : {}),
        ...(item?.roleId ? { roleId: item.roleId } : {}),
        ...(item?.editVersion ? { editVersion: item.editVersion } : { editVersion: 0 }),
        properties: {
          businessLicenseUrl: Array.isArray(item?.properties?.businessLicenseUrl) ? item?.properties?.businessLicenseUrl?.[0]?.url : item?.properties?.businessLicenseUrl || '',
          telephoneNumber: item?.properties?.telephoneNumber || '',
          address: item?.properties?.address,
          bankName: item?.properties?.bankName,
          bankAccount: item?.properties?.bankAccount,
          invoiceAddress: item?.properties?.invoiceAddress,
          invoiceEmail: item?.properties?.invoiceEmail,
          invoicePerson: item?.properties?.invoicePerson,
          invoiceTelephoneNumber: item?.properties?.invoiceTelephoneNumber,
          // 收款账户信息不回传给后端接口
        }
      }))
    };

    const params = (mode === 'edit' || formData?.roleId) ? editParams : saveParams;
    return addEnterprise(params as any).then((data) => {
      if (isTrue(data?.success)) {
        if (!skipSuccessMessage) {
          Message.success('保存成功');
          onClose();
          setTimeout(() => {
            onRefresh();
          }, 1000);
        }
        return data;
      } else {
        Message.error(data?.message || '保存失败');
        return Promise.reject(new Error(data?.message || '保存失败'));
      }
    });
  };

  const handleAudit = () => {
    setAuditDialogVisible(true);
  };

  const handleApprove = async () => {
    const formData = fields.getValues();
    try {
      const res = await auditAll({
        instanceType: 'bpms',
        targetType: 'enterprise',
        auditType: 'approve',
        targetIdList: [String(formData?.roleId)],
      });
      if (isTrue(res?.success)) {
        Message.success('操作已提交,审核状态有一定延迟,请以bpms页面结果为准。');
        onClose();
        setTimeout(() => {
          onRefresh();
        }, 1000);
      } else {
        Message.error(res?.message || '审核通过失败');
      }
    } catch (error) {
      Message.error(error?.message || '审核通过失败');
    }
  };

  const handleReject = async (remark: string) => {
    const formData = fields.getValues();
    try {
      const res = await auditAll({
        instanceType: 'bpms',
        targetType: 'enterprise',
        auditType: 'reject',
        targetIdList: [String(formData?.roleId)],
        mark: remark
      });
      if (isTrue(res?.success)) {
        Message.success('操作已提交,审核状态有一定延迟,请以bpms页面结果为准。');
        onClose();
        setTimeout(() => {
          onRefresh();
        }, 1000);
      } else {
        Message.error(res?.message || '审核不通过失败');
      }
    } catch (error) {
      Message.error(error?.message || '审核不通过失败');
    }
  };

  const handleCancelAudit = () => {
    const formData = fields.getValues();
    auditAll({
      instanceType: 'bpms',
      targetType: 'enterprise',
      auditType: 'cancel',
      targetIdList: [String(formData?.roleId)],
    }).then((data) => {
      if (isTrue(data?.success)) {
        Message.success('操作已提交,审核状态有一定延迟,请以bpms页面结果为准。');
        onClose();
        setTimeout(() => {
          onRefresh();
        }, 1000);
      } else {
        Message.error(data?.message || '撤回审核失败');
      }
    });
  };

  return (
    <Drawer
      title={titleMap?.[mode]}
      width={1000}
      visible={visible && (!isLoading && isDataLoaded)}
      onClose={onClose}
    >
      <div className={styles.subEnterpriseManage}>
        {mode !== 'add' && (
          <div className={styles.statusContainer}>
            <div className={styles.statusItem}>
              <span className={styles.statusLabel}>企业状态:</span>
              <ListStatusTag status={fields.getValue('status') as string} />
            </div>
            <div className={styles.statusItem}>
              <span className={styles.statusLabel}>审核状态:</span>
              <StatusTag value={fields.getValue('auditStatus') as number} />
              {fields.getValue('auditUrl') ? <a className={styles.auditUrl} href={fields.getValue('auditUrl') as string} target="_blank" rel="noopener noreferrer">查看审批流</a> : null}
            </div>
          </div>
        )}
        <Form field={fields} labelAlign="left" useLabelForErrorMessage>
          <EnterpriseInfoForm
            fields={fields}
            mode={mode}
          />
          <SubEnterpriseList
            mode={mode}
            editTableDataSource={editTableDataSource}
            setEditTableDataSource={setEditTableDataSource}
            handleAdd={handleAdd}
            handleEdit={handleEdit}
            handleDelete={handleDelete}
            handleView={handleView}
          />
          <ActionButtons
            handleSaveForm={async () => {
              if (mode === 'add') {
                // 保存时不校验所有必填项，只校验企业名称和社会信用代码
                const saveResponse = await handleSaveForm({ skipSuccessMessage: true, validateOnlyRequired: true });
                const enterpriseDetail = await queryEnterpriseDetail({ roleId: Number(saveResponse?.data?.roleId) });
                if (saveResponse.data.roleId) {
                  fields.setValues({
                    roleId: saveResponse.data.roleId,
                    editVersion: enterpriseDetail?.data?.editVersion,
                  });
                  // 更新子企业数据，将query接口获取到的roleId更新到子企业数据中
                  let updatedSubList = editTableDataSource;
                  if (enterpriseDetail?.data?.subList) {
                    updatedSubList = editTableDataSource.map((item, index) => {
                      const querySubItem = enterpriseDetail.data.subList[index];
                      return {
                        ...item,
                        roleId: querySubItem?.roleId || item.roleId, // 使用query接口获取到的roleId
                        editVersion: querySubItem?.editVersion || item.editVersion,
                      };
                    });
                    setEditTableDataSource(updatedSubList);
                  }
                  // 直接使用更新后的数据进行第二次保存，避免异步状态更新问题
                  await handleSaveForm({ skipSuccessMessage: false, validateOnlyRequired: true }, updatedSubList);
                } else {
                  await handleSaveForm({ skipSuccessMessage: false, validateOnlyRequired: true });
                }
              } else {
                handleSaveForm({ skipSuccessMessage: false });
              }
            }}
            handleSubmit={handleSubmit}
            handleCancelAudit={handleCancelAudit}
            handleAudit={handleAudit}
            operationList={fields.getValue('operationList') || []}
            mode={mode}
          />
        </Form>
        <SubEnterpriseDialog
          isVisible={subEnterpriseDialogVisible}
          toggleVisibility={handleDialogClose}
          mode={subEnterpriseDialogMode}
          initialValues={editTableDataSource?.[subEnterpriseDialogEditIndex] || undefined}
          mainEnterpriseName={fields.getValue('name') || ''}

          onSubmit={(formData, _mode) => {
            if (_mode === 'add') {
              setEditTableDataSource([...editTableDataSource, formData]);
            } else {
              const newDataSource = [...editTableDataSource];
              newDataSource[subEnterpriseDialogEditIndex] = {
                ...newDataSource[subEnterpriseDialogEditIndex],
                properties: {
                  ...formData?.properties,
                  ...formData
                },
                ...formData,
              };
              setEditTableDataSource(newDataSource);
            }
          }}
        />
        <ReviewModal
          visible={auditDialogVisible}
          onClose={() => setAuditDialogVisible(false)}
          onApprove={handleApprove}
          onReject={handleReject}
          modalTitle="企业审核"
        />
      </div>
    </Drawer>
  );
};

export default SubEnterpriseManage;
