import React from 'react';
import { Table, Button } from '@alifd/next';
import { SubEnterpriseItem } from '@/types';
import styles from './index.module.scss';
import { formatTimestamp } from '@/pages/Utils';

interface SubEnterpriseListProps {
  mode: 'edit' | 'add' | 'view';
  editTableDataSource: SubEnterpriseItem[];
  setEditTableDataSource: React.Dispatch<React.SetStateAction<SubEnterpriseItem[]>>;
  handleAdd: () => void;
  handleEdit: (index: number) => void;
  handleDelete: (index: number) => void;
  handleView: (index: number) => void;
}

const SubEnterpriseList: React.FC<SubEnterpriseListProps> = ({
  mode,
  editTableDataSource,
  handleAdd,
  handleEdit,
  handleDelete,
  handleView
}) => {
  return (
    <div className={styles.formItemGroup} style={{ paddingBottom: 16 }}>
      <div className={styles.groupTitle}>子企业列表</div>
      <div className={styles.gridContainer2}>
        <div className={styles.editTableContainer}>
          <div className={styles.groupSubtitle}>
            {mode === 'view' ? null :
            <Button type="primary" text onClick={handleAdd}>
                新增子企业
              </Button>
            }
          </div>
          <div>
            <Table dataSource={editTableDataSource} emptyContent={<div>暂无数据</div>}>
              <Table.Column
                title="企业id"
                dataIndex="roleId"
              />
              <Table.Column
                title="统一社会信用代码"
                dataIndex="outId"
              />
              <Table.Column
                title="企业名称"
                dataIndex="name"
              />
              <Table.Column
                title="创建时间"
                dataIndex="gmtCreate"
                cell={formatTimestamp}
              />
              {mode === 'view' ?  <Table.Column
                width="150px"
                title="操作"
                cell={(value, index) => (
                  <div style={{ display: 'flex' }}>
                    <Button text type="primary" onClick={() => handleView(index)} style={{ marginRight: '10px' }}>
                      查看
                    </Button>
                  </div>
                )}
              /> :
              <Table.Column
                width="150px"
                title="操作"
                cell={(value, index) => (
                  <div style={{ display: 'flex' }}>
                    <Button text type="primary" onClick={() => handleEdit(index)} style={{ marginRight: '10px' }}>
                      编辑
                    </Button>
                    <Button text type="primary" onClick={() => handleDelete(index)} style={{ marginRight: '10px' }}>
                      删除
                    </Button>
                  </div>
                )}
              />
            }
            </Table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubEnterpriseList; 