import React from 'react';
import { Input, Upload, Form, NumberPicker } from '@alifd/next';
import { adapters } from '@ali/bzb-request';
import { validateEmail, validatePhoneNumber, validateSocialCreditCode } from '@/pages/Utils';
import styles from './index.module.scss';

interface EnterpriseInfoFormProps {
    fields: any;
    mode: 'edit' | 'add' | 'view';
}

const EnterpriseInfoForm: React.FC<EnterpriseInfoFormProps> = ({ fields, mode }) => {
    return (
        <div className={styles.formItemGroup}>
            <div className={styles.groupTitle}>企业信息</div>
            <div className={styles.gridContainer}>
                {mode === 'edit' &&
                    <Form.Item
                        name="roleId"
                        label="企业id"
                        required={true}
                        labelCol={{ fixedSpan: 7 }}
                        labelAlign="left"
                        colon
                        isPreview={mode === 'edit' || mode === 'view'}
                    >
                        <Input placeholder="请输入" className={styles.inputWidth} maxLength={100} />
                    </Form.Item>
                }
                <Form.Item
                    name="name"
                    label="企业全称"
                    required={true}
                    labelCol={{ fixedSpan: 7 }}
                    labelAlign="left"
                    colon
                    isPreview={mode === 'edit' || mode === 'view'}
                >
                    <Input placeholder="请输入" className={styles.inputWidth} maxLength={100} />
                </Form.Item>
                <Form.Item
                    name="outId"
                    label="统一社会信用代码"
                    required={true}
                    labelCol={{ fixedSpan: 7 }}
                    labelAlign="left"
                    colon
                    isPreview={mode === 'edit' || mode === 'view'}
                    validator={(rule, value) => validateSocialCreditCode(value as string)}
                >
                    <Input placeholder="请输入" className={styles.inputWidth} maxLength={18} />
                </Form.Item>
                {mode !== 'view' ? (
                    <Form.Item label="营业执照" required name='businessLicenseUrl' labelCol={{ fixedSpan: 7 }} labelAlign="left" colon>
                        <Upload
                            shape="card"
                            limit={1}
                            accept='image/*'
                            request={adapters.upload}
                            className={styles.formItemGroupEnterpriseInfoBusinessLicense}
                            listType="card"
                            defaultValue={fields?.getValue('businessLicenseUrl')?.filter((item: any) => item.url) || []}
                            value={fields?.getValue('businessLicenseUrl')?.filter((item: any) => item.url) || []}
                            onSuccess={(info) => {
                                const picUrl = 'https://xiaoer.alibaba-inc.com/cobweb/upload/preview/' + info?.response?.path;
                                const picData = {
                                    uid: '0',
                                    name: 'IMG.png',
                                    state: 'done',
                                    url: picUrl,
                                    downloadURL: picUrl,
                                    imgURL: picUrl,
                                };
                                fields.setValue('businessLicenseUrl', [picData]);
                            }}
                        >
                            上传图片
                        </Upload>
                    </Form.Item>
                ) : (
                    <Form.Item
                        label="营业执照"
                        name='businessLicenseUrl'
                        labelCol={{ fixedSpan: 7 }}
                        labelAlign="left"
                        colon
                        isPreview
                        required
                    >
                        {fields?.getValue('businessLicenseUrl')?.[0]?.url ? (
                            <img
                                src={fields.getValue('businessLicenseUrl')[0].url}
                                alt="营业执照"
                                style={{ maxWidth: '200px' }}
                            />
                        ) : '暂无图片'}
                    </Form.Item>
                )}
                <Form.Item
                    name="telephoneNumber"
                    label="购方电话"
                    required={true}
                    labelCol={{ fixedSpan: 7 }}
                    labelAlign="left"
                    colon
                    isPreview={mode === 'view'}
                    validator={(rule, value) => validatePhoneNumber(value as string)}
                >
                    <Input
                        // @ts-ignore
                        placeholder="请输入购方电话"
                        className={styles.inputWidth}
                        maxLength={13}
                        onInput={(e: any) => {
                            // Remove any non-digit characters except hyphen and limit to 13 characters
                            let value = e.target.value.replace(/[^\d-]/g, '');
                            if (value.length > 13) {
                                value = value.slice(0, 13);
                            }
                            e.target.value = value;
                        }}
                    />
                </Form.Item>
                <Form.Item
                    name="address"
                    label="企业地址"
                    required={true}
                    labelCol={{ fixedSpan: 7 }}
                    labelAlign="left"
                    colon
                    isPreview={mode === 'view'}
                >
                    <Input placeholder="请输入企业地址" className={styles.inputWidth} maxLength={100} />
                </Form.Item>
                <Form.Item
                    name="bankName"
                    label="开户行"
                    labelCol={{ fixedSpan: 7 }}
                    labelAlign="left"
                    colon
                    isPreview={mode === 'view'}
                >
                    <Input placeholder="请输入开户行" className={styles.inputWidth} />
                </Form.Item>
                <Form.Item
                    name="bankAccount"
                    label="开户行账号"
                    labelCol={{ fixedSpan: 7 }}
                    labelAlign="left"
                    colon
                    isPreview={mode === 'view'}
                >
                    <Input
                        placeholder="请输入开户行账号"
                        className={styles.inputWidth}
                        maxLength={19}
                        onKeyDown={(e) => {
                            // Allow only numbers, backspace, delete, arrow keys
                            if (
                                !/^\d$/.test(e.key) &&
                                !['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(e.key)
                            ) {
                                e.preventDefault();
                            }
                        }}
                        onChange={(value) => {
                            // Remove any non-digit characters from input
                            const newValue = value.replace(/\D/g, '');
                            fields.setValue('bankAccount', newValue);
                        }}
                    />
                </Form.Item>
                <Form.Item
                    name="invoiceAddress"
                    label="发票邮寄地址"
                    labelCol={{ fixedSpan: 7 }}
                    labelAlign="left"
                    colon
                    isPreview={mode === 'view'}
                >
                    <Input placeholder="请输入发票邮寄地址" className={styles.inputWidth} />
                </Form.Item>
                <Form.Item
                    name="invoiceEmail"
                    label="发票发送邮箱"
                    labelCol={{ fixedSpan: 7 }}
                    labelAlign="left"
                    colon
                    format='email'
                    isPreview={mode === 'view'}
                    validator={(rule, value) => value ? validateEmail(value as string) : Promise.resolve()}
                >
                    <Input placeholder="请输入发票发送邮箱" className={styles.inputWidth} />
                </Form.Item>
                <Form.Item
                    name="invoicePerson"
                    label="发票联系人"
                    labelCol={{ fixedSpan: 7 }}
                    labelAlign="left"
                    colon
                    isPreview={mode === 'view'}
                >
                    <Input placeholder="请输入发票联系人" className={styles.inputWidth} />
                </Form.Item>
                <Form.Item
                    name="invoiceTelephoneNumber"
                    label="发票联系人电话"
                    labelCol={{ fixedSpan: 7 }}
                    labelAlign="left"
                    colon
                    isPreview={mode === 'view'}
                    validator={(rule, value) => value ? validatePhoneNumber(value as string) : Promise.resolve()}
                >
                    {/* @ts-ignore */}
                    <Input placeholder="请输入发票联系人电话" className={styles.inputWidth} maxLength={13}
                        onInput={(e: any) => {
                            // Remove any non-digit characters except hyphen and limit to 13 characters
                            let value = e.target.value.replace(/[^\d-]/g, '');
                            if (value.length > 13) {
                                value = value.slice(0, 13);
                            }
                            e.target.value = value;
                        }}
                    />
                </Form.Item>

            </div>
            {mode !== 'add' && (
                <>
                    <div className={styles.groupTitle}>收款账户</div>
                    <div className={styles.gridContainer}>
                        <Form.Item
                            name="bankCardNo"
                            label="子户卡号"
                            labelCol={{ fixedSpan: 7 }}
                            labelAlign="left"
                            colon
                            isPreview
                        >
                            <Input className={styles.inputWidth} />
                        </Form.Item>
                        <Form.Item
                            name="bankCertName"
                            label="银行户名"
                            labelCol={{ fixedSpan: 7 }}
                            labelAlign="left"
                            colon
                            isPreview
                        >
                            <Input className={styles.inputWidth} />
                        </Form.Item>
                        <Form.Item
                            name="branchNo"
                            label="行号"
                            labelCol={{ fixedSpan: 7 }}
                            labelAlign="left"
                            colon
                            isPreview
                        >
                            <Input className={styles.inputWidth} />
                        </Form.Item>
                        <Form.Item
                            name="branchName"
                            label="开户行名称"
                            labelCol={{ fixedSpan: 7 }}
                            labelAlign="left"
                            colon
                            isPreview
                        >
                            <Input className={styles.inputWidth} />
                        </Form.Item>
                    </div>
                </>
            )}
        </div>
    );
};

export default EnterpriseInfoForm; 