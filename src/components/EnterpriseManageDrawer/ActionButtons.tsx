import React from 'react';
import OperationButton from '@/components/OperationButton';
import styles from './index.module.scss';

interface ActionButtonsProps {
    mode: 'edit' | 'add' | 'view';
    operationList: string[];
    handleSaveForm: () => void;
    handleSubmit: () => void;
    handleCancelAudit: () => void;
    handleAudit: () => void;
}

const ActionButtons: React.FC<ActionButtonsProps> = ({
    mode,
    operationList,
    handleSaveForm,
    handleSubmit,
    handleCancelAudit,
    handleAudit,
}) => {

    const operationHandlers = {
        'save': handleSaveForm,
        'submit': handleSubmit,
        'cancel': handleCancelAudit,
        'audit': handleAudit
    };

    if (mode === 'add') {
        return (
            <div className={styles.submitButton}>
                {['save', 'submit']?.map((operation: string) => {
                    return (
                        <OperationButton
                            key={operation}
                            operation={operation}
                            onClick={operationHandlers[operation]}
                        />
                    );
                })}
            </div>
        );
    }

    return (
        <div className={styles.submitButton}>
            {operationList.map((_operation: string) => {
                const operation = _operation.toLowerCase();
                return (
                    <OperationButton
                        key={operation}
                        operation={operation}
                        onClick={operationHandlers[operation]}
                    />
                );
            })}
        </div>
    );
};

export default ActionButtons; 