.subEnterpriseManage {
  padding: 10px;
  min-height: 100vh;

  .editTableAddAction {
    margin: 12px 0px;
    border: 1px dashed #dee0e5;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    color: var(--color-brand1-6, #4f89ff);
    text-align: center;
    padding: 7px 0;
  }
  .editTableContainer {
    .editTableTitle {
      color: #111;
      font-size: 18px;
      font-weight: 500;
      line-height: 26px;
    }
  }
  .tableAction {
    margin: 12px 0px;
    border: 1px dashed #dee0e5;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    color: var(--color-brand1-6, #4f89ff);
    text-align: center;
    padding: 7px 0;
  }

  .formItemGroupEnterpriseInfoBusinessLicense {
    margin-bottom: 8px;
    width: 500px;
  }

  .formItemGroup {
    .groupTitle {
      color: #111;
      font-size: 18px;
      font-weight: 500;
      line-height: 26px;
    }
    .groupSubtitle {
      color: #999999;
      font-size: 14px;
      font-weight: 400;
      line-height: 22px;
      margin-bottom: 12px;
    }
    .gridContainer {
      padding: 10px;
    }
    .gridContainer2 {
      padding: 10px;
      margin-left: 15%;
      width: 85%;
      margin-top: -33px;
    }
  }

  .inputWidth {
    width: 500px;
  }

  .submitButton {
    position: absolute;
    bottom: 0;
    width: 100%;
    border-top: 1px solid #e8e8e8;
    padding: 10px 16px;
    text-align: center;
    left: 0;
    background: #fff;
    border-radius: 0 0 4px 4px;
    gap: 10px;
    display: flex;
    justify-content: center;
  }
}

:global {
  .next-dialog-header {
    border-bottom: 1px solid #ccc;
  }
}

.statusContainer {
  padding: 16px 0;
  border-bottom: 1px solid #e8e8e8;
  margin-bottom: 24px;
  display: flex;
  align-items: baseline;
  gap: 16px;
}

.statusItem {
  display: flex;
  align-items: center;
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }
}

.statusLabel {
  font-size: 14px;
  color: #333;
  min-width: 70px;
}

.auditUrl {
  margin-left: 4px;
}
