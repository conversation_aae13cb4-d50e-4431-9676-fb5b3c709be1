import React, { useEffect } from 'react';
import { Input, Form, Field, Dialog, Upload, NumberPicker } from '@alifd/next';
import { adapters } from '@ali/bzb-request';
import { validateEmail, validatePhoneNumber, validateSocialCreditCode } from '@/pages/Utils';

import styles from './index.module.scss';

const SubEnterpriseDialog = ({ isVisible, toggleVisibility, onSubmit, mode = 'add', initialValues = {}, mainEnterpriseName }) => {
  const fields = Field.useField();
  useEffect(() => {
    if ((mode === 'edit' || mode === 'view') && initialValues && isVisible) {
      const picUrl = initialValues?.properties?.businessLicenseUrl;
      const picData = {
        uid: '0',
        name: 'IMG.png',
        state: 'done',
        url: picUrl,
        downloadURL: picUrl,
        imgURL: picUrl,
      };
      fields.setValues({ ...initialValues, ...initialValues?.properties, businessLicenseUrl: [picData] });
    }
    fields.setValues({ mainEnterpriseName });
  }, [initialValues, mode, isVisible]);

  const renderMyDialog = () => {
    return (
      <Dialog
        title={mode === 'add' ? "新增子企业" : mode === 'view' ? "查看子企业" : "编辑子企业"}
        footerAlign="center"
        visible={isVisible}
        style={{ minWidth: 720 }}
        className={styles.rcodeDialog}
        footer={ mode === 'view' ? false : undefined}
        onOk={async () => {
          // 表单校验ok
          const result = await fields.validatePromise();
          if (result?.errors) {
            return;
          }
          const formData = fields.getValues();
          if (onSubmit) {
            onSubmit({
              ...formData,
              properties: {
                businessLicenseUrl: formData.businessLicenseUrl?.[0]?.url,
                name: formData.name,
                outId: formData.outId,
                telephoneNumber: formData.telephoneNumber,
                address: formData.address,
                bankName: formData.bankName,
                bankAccount: formData.bankAccount,
                invoiceAddress: formData.invoiceAddress
              }
            }, mode);
          }
          toggleVisibility(false);
        }}
        onCancel={() => toggleVisibility(false)}
        onClose={() => toggleVisibility(false)}
      >
        <Form field={fields} labelAlign="left" labelCol={{ fixedSpan: 7 }} wrapperCol={{ span: 14 }} useLabelForErrorMessage>
          <Form.Item name="mainEnterpriseName" label="主企业" required={true} labelAlign="left" colon isPreview>
            <Input placeholder="请输入" style={{ width: '324px' }} maxLength={100} />
          </Form.Item>
          <Form.Item name="name" label="企业全称" required={true} labelAlign="left" colon disabled={mode === 'edit' || mode === 'view'}>
            <Input placeholder="请输入" style={{ width: '324px' }} maxLength={100} />
          </Form.Item>
          <Form.Item name="outId" label="统一社会信用代码" required={true} labelAlign="left" colon disabled={mode === 'edit' || mode === 'view'} validator={(rule, value) => validateSocialCreditCode(value as string)}>
            <Input placeholder="请输入" style={{ width: '324px' }} maxLength={18} />
          </Form.Item>
          <Form.Item label="营业执照" required name='businessLicenseUrl' labelCol={{ fixedSpan: 7 }} labelAlign="left" colon isPreview={mode === 'view'}>
            <Upload
              shape="card"
              limit={1}
              accept='image/*'
              request={adapters.upload}
              className={styles.formItemGroupEnterpriseInfoBusinessLicense}
              listType="card"
              defaultValue={fields?.getValue('businessLicenseUrl')}
              value={fields?.getValue('businessLicenseUrl')}
              onSuccess={(info) => {
                const picUrl = 'https://xiaoer.alibaba-inc.com/cobweb/upload/preview/' + info?.response?.path;
                const picData = {
                  uid: '0',
                  name: 'IMG.png',
                  state: 'done',
                  url: picUrl,
                  downloadURL: picUrl,
                  imgURL: picUrl,
                };
                fields.setValue('businessLicenseUrl', [picData]);
              }}
            >
              上传图片
            </Upload>
          </Form.Item>
          <Form.Item name="telephoneNumber" label="购方电话" required={true} labelAlign="left" colon validator={(rule, value) => validatePhoneNumber(value as string)} isPreview={mode === 'view'}>
            <Input
              placeholder="请输入"
              style={{ width: '324px' }}
              maxLength={13}
              onInput={(e: any) => {
                // Remove any non-digit characters and limit to 20 digits
                let value = e.target.value.replace(/[^\d-]/g, '');
                if (value.length > 13) {
                  value = value.slice(0, 13);
                }
                e.target.value = value;
              }}
            />
          </Form.Item>
          <Form.Item name="address" label="企业地址" required={true} labelAlign="left" colon isPreview={mode === 'view'}>
            <Input placeholder="请输入企业地址" style={{ width: '324px' }} />
          </Form.Item>
          <Form.Item name="bankName" label="开户行"  labelAlign="left" colon isPreview={mode === 'view'}>
            <Input placeholder="请输入开户行" style={{ width: '324px' }} />
          </Form.Item>
          <Form.Item name="bankAccount" label="开户行账号"  labelAlign="left" colon isPreview={mode === 'view'}>
            <Input
              placeholder="请输入开户行账号"
              style={{ width: '324px' }}
              maxLength={19}
              onKeyDown={(e) => {
                // Allow only numbers, backspace, delete, arrow keys
                if (
                  !/^\d$/.test(e.key) &&
                  !['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'Tab'].includes(e.key)
                ) {
                  e.preventDefault();
                }
              }}
              onChange={(value) => {
                // Remove any non-digit characters from input
                const newValue = value?.replace(/\D/g, '');
                fields.setValue('bankAccount', newValue);
              }}
            />
          </Form.Item>
          <Form.Item name="invoiceAddress" label="发票邮寄地址" labelAlign="left" colon isPreview={mode === 'view'}>
            <Input placeholder="请输入发票邮寄地址" style={{ width: '324px' }} />
          </Form.Item>
          <Form.Item
            name="invoiceEmail"
            label="发票发送邮箱"
            labelAlign="left"
            colon
            validator={(rule, value) => value ? validateEmail(value as string) : Promise.resolve()}
            isPreview={mode === 'view'}
          >
            <Input placeholder="请输入发票发送邮箱" style={{ width: '324px' }} />
          </Form.Item>
          <Form.Item name="invoicePerson" label="发票联系人" labelAlign="left" colon isPreview={mode === 'view'}>
            <Input placeholder="请输入发票联系人" style={{ width: '324px' }} />
          </Form.Item>
          <Form.Item name="invoiceTelephoneNumber" label="发票联系人电话" labelAlign="left" colon validator={(rule, value) => value ? validatePhoneNumber(value as string) : Promise.resolve()} isPreview={mode === 'view'}>
            <Input
              placeholder="请输入发票联系人电话"
              style={{ width: '324px' }}
              maxLength={13}
              onInput={(e: any) => {
                // Remove any non-digit characters and limit to 20 digits
                let value = e.target.value.replace(/[^\d-]/g, '');
                if (value.length > 13) {
                  value = value.slice(0, 13);
                }
                e.target.value = value;
              }}
            />
          </Form.Item>
        </Form>
      </Dialog>
    );
  };

  return <div className={styles.myPage848}>{renderMyDialog()}</div>;
};

export default SubEnterpriseDialog;
