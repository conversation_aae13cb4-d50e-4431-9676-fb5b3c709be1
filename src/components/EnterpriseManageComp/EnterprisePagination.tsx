import React from 'react';
import { Pagination } from '@alifd/next';
import styles from '@/pages/EnterpriseManage/index.module.scss';

interface EnterprisePaginationProps {
  pagination: any;
  onChange: (current: number, pageSize: number) => void;
}

const EnterprisePagination: React.FC<EnterprisePaginationProps> = ({ pagination, onChange }) => {
  return (
    <div>
      <Pagination
        {...pagination}
        onChange={(current) => onChange(current, pagination?.pageSize || 20)}
        onPageSizeChange={(size) => onChange(1, size)}
        className={styles.myPagination}
        type="normal"
        shape="normal"
        showJump
        pageSizeSelector="dropdown"
        pageSizeList={[10, 20]}
        totalRender={(total) => <span className={styles.totalText}> 共 {total} 条</span>}
      />
    </div>
  );
};

export default EnterprisePagination; 