import React from 'react';
import { Dialog, Message } from '@alifd/next';
import OperationButton from '@/components/OperationButton';
import { deleteEnterprise, invalidEnterprise } from '@/services';
import { isTrue } from '@/pages/Utils';

interface ActionButtonsProps {
  record: any;
  onRefresh: () => void;
  onEdit: (record: any) => void;
  onView: (record: any) => void;
}

const ActionButtons: React.FC<ActionButtonsProps> = ({ record, onRefresh, onEdit, onView }) => {
  const operationMap = {
    EDIT: {
      text: '编辑',
      onClick: () => onEdit(record),
    },
    DELETE: {
      text: '删除',
      onClick: () => {
        Dialog.confirm({
          title: '确认删除',
          content: `确定要删除企业 "${record.name}" 吗？`,
          onOk: async () => {
            try {
              const result = await deleteEnterprise({
                roleId: record.roleId,
                editVersion: record.editVersion,
              });
              if (isTrue(result?.success)) {
                setTimeout(() => onRefresh(), 1000);
                Message.success("删除成功");
              } else {
                Message.error(result?.message || "删除失败");
              }
            } catch (error) {
              console.error("Failed to delete enterprise:", error);
            }
          }
        });
      }
    },
    INVALIDATE: {
      text: '失效',
      onClick: () => {
        Dialog.confirm({
          title: '确认失效',
          content: `失效后会导致该企业下的所有项目无法下单，是否继续？`,
          okProps: {
            children: '继续失效操作'
          },
          onOk: async () => {
            try {
              const result = await invalidEnterprise({
                roleId: record.roleId,
                editVersion: record.editVersion,
              });
              if (isTrue(result?.success)) {
                setTimeout(() => onRefresh(), 1000);
                Message.success("操作成功");
              } else {
                Message.error(result?.message || "操作失败");
              }
            } catch (error) {
              console.error("Failed to invalidate enterprise:", error);
            }
          }
        });
      }
    },
    VIEW: {
      text: '查看',
      onClick: () => onView(record),
    },
    CREATE_PROJECT: {
      text: '新建项目',
      onClick: () => {
        window.open(`/biz-purchase/project-manage?enterpriseId=${record.roleId}&type=add`, '_blank');
      }
    }
  };

  return (
    <div>
      {record.operationList?.map((_operation: string) => {
        const operation = _operation.toLowerCase();
        return (
          <OperationButton
            key={operation}
            isText={true}
            style={{ marginRight: "5px" }}
            operation={operation}
            text={operationMap[_operation]?.text}
            onClick={operationMap[_operation]?.onClick}
          />
        );
      })}
    </div>
  );
};

export default ActionButtons; 