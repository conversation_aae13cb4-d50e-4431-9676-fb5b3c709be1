import React from 'react';
import { Button, Tag } from '@alifd/next';
import { EnterpriseAuditStatus } from '@/pages/Utils';

interface StatusTagProps {
    value: number;
    record?: any
}

// 兼容项目审核状态 和 企业审核状态
const StatusTag: React.FC<StatusTagProps> = ({ value, record }) => {
    const statusMapping = {
        [EnterpriseAuditStatus.INIT.value]: { color: "gray", text: EnterpriseAuditStatus.INIT.desc },
        [EnterpriseAuditStatus.AUDITING.value]: { color: "orange", text: EnterpriseAuditStatus.AUDITING.desc },
        [EnterpriseAuditStatus.SUCCESS.value]: { color: "green", text: EnterpriseAuditStatus.SUCCESS.desc },
        [EnterpriseAuditStatus.FAIL.value]: { color: "red", text: EnterpriseAuditStatus.FAIL.desc },
    };
    const status = statusMapping[value] || { color: "gray", text: "未知状态" };

    return (
        <>
            <Tag type="normal" size="small" color={status.color}>
                {status.text}
            </Tag>
            <br />
            {
                record?.auditUrl &&
                <Button text type="primary" style={{ marginTop: 4 }} onClick={() => {
                    window.open(record?.auditUrl, '_blank');
                }}> 查看审批流 </Button>
            }
        </>
    );
};

export default StatusTag; 