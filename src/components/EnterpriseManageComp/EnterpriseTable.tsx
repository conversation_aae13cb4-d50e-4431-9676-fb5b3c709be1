import React from 'react';
import { Table, Button } from '@alifd/next';
import styles from '@/pages/EnterpriseManage/index.module.scss';
import ActionButtons from './ActionButtons';
import StatusTag from './StatusTag';
import ListStatusTag from '@/components/ListStatusTag';
import { formatTimestamp } from '@/pages/Utils';
import AdaptiveTable from '@/components/AdaptiveTable';

interface EnterpriseTableProps {
  loading: boolean;
  dataSource: any[];
  onAddEnterprise: () => void;
  onEdit: (record: any) => void;
  onView: (record: any) => void;
  onRefresh: () => void;
}

const EnterpriseTable: React.FC<EnterpriseTableProps> = ({
  loading,
  dataSource,
  onAddEnterprise,
  onEdit,
  onView,
  onRefresh
}) => {
  const renderProjectList = (value, index, record) => {
    if (Array.isArray(record?.projectList)) {
      return record?.projectList?.map((project, index, array) => (
        <>
        <a key={index} href={`/bzb/noone/biz-purchase/project-manage?projectId=${project?.projectId}`} target="_blank" rel="noopener noreferrer">
          <span className={styles.projectItem} title={project?.projectName}>
            {project?.projectName.replace(/\s/g, '')}
          </span>
        </a>
        <br />
        </>
      ));
    }
    return null;
  };

  return (
    <div className={styles.cardEnterpriseList}>
      <div className={styles.cardTitleContainer}>
        <span className={styles.cardTitle}>企业列表</span>
        <Button
          className={styles.cardEnterpriseListButton}
          type="primary"
          onClick={onAddEnterprise}
        >
          新增企业
        </Button>
      </div>
      <div className={styles.listContainer}>
        <AdaptiveTable 
          adaptiveThreshold={1500}
          loading={loading} 
          dataSource={dataSource} 
          primaryKey="roleId" 
          emptyContent={<div> 暂无数据 </div>}
        >
          <Table.Column title="企业id" dataIndex="roleId" width={100} lock="left" />
          <Table.Column title="企业简称" dataIndex="name" width={150} lock="left" />
          <Table.Column title="子企业数" dataIndex="subListCount" width={90} />
          <Table.Column title="关联项目" dataIndex="projectList" cell={renderProjectList} width={240} />
          <Table.Column title="创建时间" dataIndex="gmtCreate" cell={formatTimestamp} width={160} />
          <Table.Column title="最近编辑时间" dataIndex="gmtModified" cell={formatTimestamp} width={160} />
          <Table.Column title="最近一次编辑者" dataIndex="updateOperator" width={150} />
          <Table.Column
            title="企业状态"
            dataIndex="status"
            cell={(value) => <ListStatusTag status={value} />}
            width={120}
            lock="right"
            key="status"
          />
          <Table.Column
            title="审核状态"
            dataIndex="auditStatus"
            cell={(value, index, record) => <StatusTag value={value} record={record} />}
            width={140}
            lock="right"
            key="auditStatus"
          />
          <Table.Column
            title="操作"
            key="action"
            cell={(value, index, record) => (
              <ActionButtons
                record={record}
                onRefresh={onRefresh}
                onEdit={onEdit}
                onView={onView}
              />
            )}
            width={200}
            lock="right"
          />
        </AdaptiveTable>
      </div>
    </div>
  );
};

export default EnterpriseTable; 