import React from "react";
import { Button } from "@alifd/next";

interface OperationButtonProps {
    text?: string
    isText?: boolean;
    operation: string;
    onClick: () => void;
    style?: React.CSSProperties;
}

const OperationButton: React.FC<OperationButtonProps> = ({ operation, onClick, style, isText =false, text }) => {
    const buttonTextMap = {
        'save': '保存',
        'submit': '提交审核',
        'cancel': '撤回审核',
        'audit': '审核'
    };

    return (
        <Button
            type={operation === 'save' ? 'normal' : 'primary'}
            onClick={onClick}
            text={isText}
            style={{ marginRight: '10px', ...style }}
        >
            {text || buttonTextMap[operation]}
        </Button>
    );
};

export default OperationButton;