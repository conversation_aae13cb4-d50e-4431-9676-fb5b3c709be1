.pooledProductList {
  padding: 10px;
  min-height: 100vh;
  .nextFormItem {
    margin-bottom: 10px;
    .nextFormItemControl {
      .nextSelect {
        width: 100%;
        input {
          width: 100%;
        }
      }
    }
  }

  .searchBtnsButton {
    margin-right: 5px;
    margin-right: 10px;
  }

  .rcodeSearchBtns {
    display: flex;
    align-items: center;
  }

  .listBtnsButton {
    margin-right: 5px;
    margin-right: 10px;
  }

  .listBtns {
    margin: 10px 0px;
    display: flex;
    justify-content: flex-end;
  }

  .imageInfoContainer {
    display: flex;

    .imageContainer {
      width: 96px;
      height: 96px;
      border-radius: 6px;

      img {
        width: 96px;
        height: 96px;
        border-radius: 6px;
      }
    }

    .infoContainer {
      margin-left: 13px;

      .title {
        height: 44px;
        font-family: PingFangSC;
        font-weight: 500;
        font-size: 14px;
        color: #4f89ff;
        letter-spacing: 0;
        line-height: 22px;
        margin-bottom: 16px;
        // 最多两行超出省略
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        overflow: hidden;
        width: 158px;
      }

      span {
        font-family: PingFangSC;
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        letter-spacing: 0;
        line-height: 22px;
      }
    }
  }

  .myPagination {
    margin-top: 10px;
    display: flex;
    flex-grow: 1;
    justify-content: flex-end;
    .nextPaginationPages {
      margin-left: auto;
    }
  }

  .emptyContent {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .ellipsisSixLine {
    display: -webkit-box;
    -webkit-line-clamp: 6;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}

.operationRecord {
  :global {
    .next-dialog-header {
      border-bottom: 1px solid #ccc;
    }
  }

  .myPagination {
    display: flex;
    justify-content: flex-end;
    margin-top: 8px;
  }

  .editTableAddAction {
    margin: 12px 0px;
    border: 1px dashed #dee0e5;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    color: var(--color-brand1-6, #4f89ff);
    text-align: center;
    padding: 7px 0;
  }
  .editTableContainer {
    .editTableTitle {
      color: #111;
      font-size: 18px;
      font-weight: 500;
      line-height: 26px;
    }
  }
  .skuListTitle {
    color: #000000;
    font-size: 18px;
    font-weight: bold;
  }

  .selectedSkuTitle {
    color: #000000;
    font-size: 18px;
    font-weight: bold;
  }
}

.productPoolRule {
  :global {
    .next-dialog-header {
      border-bottom: 1px solid #ccc;
    }
  }
  span[data-meta='Field'] {
    width: 100%;
  }
  .next-dialog-body {
    .next-form-item-control {
      display: flex;

      .next-select {
        flex-grow: 1;
        input {
          width: 100%;
        }
      }
    }
  }
  .textNote {
    margin: 16px 0;
    color: #999999;
    font-size: 12px;
  }

  .groupTitle {
    color: #111;
    font-size: 16px;
    font-weight: 500;
    line-height: 26px;
  }

  .formItemGroup {
    .gridContainer {
      padding: 10px;
    }
  }
}
