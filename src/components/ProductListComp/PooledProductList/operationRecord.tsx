import React, { useState, useEffect } from 'react';
import { Table, Dialog, Pagination } from '@alifd/next';
import { queryItemOperateLog } from '@/services';

import styles from './index.module.scss';

enum OperationMode {
  SINGLE_PRODUCT = 'SINGLE_PRODUCT',
  PRODUCT_POOL = 'PRODUCT_POOL',
}

interface OperationRecordProps {
  poolId: number;
  mode: OperationMode;
  visible: boolean;
  onClose?: () => void;
}

const OperationRecord: React.FC<OperationRecordProps> = ({ poolId, mode, visible, onClose }) => {
  const [dataSource, setDataSource] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 });

  const handleClose = () => {
    onClose?.();
    setDataSource([]);
    setPagination({ current: 1, pageSize: 10, total: 0 });
  };

  const fetchOperationLogs = (pagination) => {
    setLoading(true);
    queryItemOperateLog({
      poolId,
      currentPage: pagination.current,
      pageSize: pagination.pageSize,
    }).then((res) => {
      setDataSource(res?.data?.dataList || []);
      setPagination((prev) => ({ ...prev, total: res?.data?.totalCount || 0 }));
    }).finally(() => {
      setLoading(false);
    });
  };

  useEffect(() => {
    if (visible) {
      fetchOperationLogs(pagination);
    }
  }, [visible, poolId, mode, pagination.current, pagination.pageSize]);

  const renderMyDialog = () => {
    return (
      <Dialog
        v2
        title={'操作记录'}
        footerAlign="right"
        visible={visible}
        footer={false}
        width={1000}
        className={styles.operationRecord}
        onClose={handleClose}
      >
        <div className="card-container">
          <Table 
            loading={loading}
            dataSource={dataSource} 
            primaryKey="my_id"
            style={{ width: '100%' }}
          >
            <Table.Column title="时间" dataIndex="operateTime" width={160} />
            <Table.Column title="操作人" dataIndex="operator" width={100} />
            <Table.Column title="行为" dataIndex="operation" width={100} />
            <Table.Column title="数据" dataIndex="details" width={150} />
          </Table>
          <Pagination
            className={styles.myPagination}
            type="normal"
            shape="normal"
            showJump
            pageSizeSelector="dropdown"
            pageSizeList={[10, 20]}
            defaultCurrent={1}
            current={pagination.current}
            total={pagination.total}
            pageSize={pagination.pageSize}
            onPageSizeChange={(pageSize) => {
              setPagination((prev) => ({
                ...prev,
                pageSize,
              }));
            }}
            onChange={(current) => {
              setPagination((prev) => ({
                ...prev,
                current,
              }));
            }}
            totalRender={(total) => `共 ${total} 条`}
          />
        </div>
      </Dialog>
    );
  };

  return <div>{renderMyDialog()}</div>;
};

export default OperationRecord;
