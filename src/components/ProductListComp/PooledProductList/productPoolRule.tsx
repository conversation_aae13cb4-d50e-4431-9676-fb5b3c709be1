import React, { useEffect } from 'react';
import { Select, Form, Field, Dialog, NumberPicker, Input } from '@alifd/next';

import { editItemsPoolRule } from '@/services';
import { IEditItemsPoolRuleParams } from '@/types';
import styles from './index.module.scss';

interface ProductPoolRuleProps {
  visible: boolean;
  onCancel?: () => void;
  poolRule?: IEditItemsPoolRuleParams;
  poolBaseInfo: any;
  onSuccess?: () => void;
}

const ProductPoolRule: React.FC<ProductPoolRuleProps> = ({ visible, onCancel, poolBaseInfo, onSuccess }) => {
  const poolRule = poolBaseInfo?.poolRule;
  const fields = Field.useField();

  useEffect(() => {
    if (poolRule && visible) {
      fields.setValues({...poolRule });
    }
    if (poolBaseInfo?.projectName  && visible ) {
        fields.setValue('project', poolBaseInfo?.projectName);
    }
  }, [poolRule, visible]);

  const handleOk = async () => {
    const values = fields.getValues();
    try {
      const response = await editItemsPoolRule({
        shipTime: Number(values.shipTime),
        discountRateReference: Number(values.discountRateReference),
        discountRateOrigin: Number(values.discountRateOrigin),
        poolId: poolBaseInfo?.poolId,
      });
      if(response?.success){
        Dialog.success({ title: '提示', content: '规则更新成功。', type: 'success' });
        onCancel?.();
        setTimeout(() => {
          poolBaseInfo.fetchPoolBaseInfo();
          onSuccess?.();
        }, 1000);
      }else{
        Dialog.error({ title: '提示', content: '规则更新失败。', type: 'warn' });
      }
    } catch (error) {
      Dialog.error({ title: '提示', content: '规则更新失败。', type: 'warn' });
    }
  };

  const handleCancel = () => {
    onCancel?.();
  };

  const renderMyDialog = () => {
    return (
      <Dialog
        v2
        title="商品入池规则"
        footerAlign="right"
        visible={visible}
        className={styles.productPoolRule}
        style={{ width: '580px' }}
        onOk={handleOk}
        onCancel={handleCancel}
        onClose={handleCancel}
      >
        <Form field={fields} labelAlign="left">
          <Form.Item name="project" label="归属项目" labelCol={{ span: 8 }} colon>
            <Input placeholder="请输入" disabled />
          </Form.Item>
          <div className={styles.formItemGroup} style={{ margin: '16px 0' }}>
            <div className={styles.groupTitle}>价格力</div>
            <div className="grid-container">
              <Form.Item name="discountRateReference" label="(专属价/普惠券后价)折扣率<=" labelCol={{ span: 8 }} colon>
                <NumberPicker max={0.99} min={0} step="0.01" />
              </Form.Item>
              <Form.Item name="discountRateOrigin" label="(专属价/市场价)折扣率<=" labelCol={{ span: 8 }} colon>
                <NumberPicker max={0.99} min={0}  step="0.01" />
              </Form.Item>
            </div>
          </div>
          <div className={styles.formItemGroup} style={{ margin: '16px 0' }}>
            <div className={styles.groupTitle}>服务&履约能力</div>
            <div className="grid-container">
              <Form.Item name="shipTime" label="发货时效<=" labelCol={{ span: 8 }} colon>
                <NumberPicker innerAfter="小时" min={0} />
              </Form.Item>
            </div>
          </div>
          <Form.Item name="name3" label="">
            <p className={styles.textNote}>
              注:每次商品数据更新,将按照此页面规则对该项目所属商品池数据做全量更新;不符合规则商品将会被剔除,请谨慎填写
            </p>
          </Form.Item>
        </Form>
      </Dialog>
    );
  };

  return <div>{renderMyDialog()}</div>;
};

export default ProductPoolRule;
