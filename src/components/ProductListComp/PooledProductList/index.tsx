import React, { useState, useEffect, useRef } from 'react';
import { Button, Tag, Table, Pagination, Dialog, Field, Message, Balloon, Icon } from '@alifd/next';
import OperationRecord from './operationRecord';
import SkuDetail from '@/components/ItemPoolComp/skuDetail';
import SearchForm from './searchForm';
import ProductPoolRule from './productPoolRule';

import { itemOnline, itemOffline, queryItemsList, queryItemInfo } from '@/services';
import { egoItemStatusOptions, formatTimestamp, isTrue } from '@/pages/Utils';
import { IQueryItemsListParams, queryItemslListResult, OperationMode, IItemOperateParams, Product } from '@/types';

import styles from './index.module.scss';

const PooledProductList = ({ poolBaseInfo, setActiveTab }) => {
  const fields = Field.useField();
  const [isOpLogDialogShow, setIsOpLogDialogShow] = useState(false);
  const [opLogMode, setOpLogMode] = useState<OperationMode>();
  const [isSkuDetailVisible, setIsSkuDetailVisible] = useState(false);
  const [isPooledRuleVisible, setIsPooledRuleVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<Product[]>([]);
  const [pagination, setPagination] = useState({ current: 1, pageSize: 20, total: 0 });
  const currentRecordRef = useRef<Product>(null);
  const [selectedRows, setSelectedRows] = useState<Product[]>([]);

  const renderActions = (value: any, index: number, record: Product) => {
    return (
      <div className={styles.actions}>
        <Button
          type="primary"
          text
          onClick={() => {
            queryItemInfo({
              poolId: poolBaseInfo?.poolId,
              itemId: record?.itemId
            }).then(res => {
              currentRecordRef.current = {
                ...res?.data,
                poolId: poolBaseInfo?.poolId,
                ...record,
                skuList: (res?.data?.skuList || record?.skuList || []).filter(Boolean)
              };
              setIsSkuDetailVisible(true);
            })
          }}
          style={{ marginRight: '5px' }}
        >
          编辑
        </Button>

        {['offline', 'online_fail'].includes(record?.egoItemStatus) && (
          <Button
            type="primary"
            text
            onClick={async () => {
              Dialog.confirm({
                title: '提示',
                content: (
                  <div
                    dangerouslySetInnerHTML={{
                      __html: `确定要上架吗？<span style="color: red;"> ${record?.title}(${record?.itemId})</span>`,
                    }}
                  />
                ),
                onOk: async () => {
                  const params: IItemOperateParams = {
                    poolId: poolBaseInfo.poolId,
                    itemIdList: [record.itemId],
                  };
                  const res = await itemOnline(params);
                  if (isTrue(res.success)) {
                    Message.success('上架成功');
                    setTimeout(() => {
                      handleSearch({ ...fields.getValues() });
                    }, 1000);
                  } else {
                    Message.error(res?.message || '上架失败' );
                  }
                },
              });
            }}
            style={{ marginRight: '5px' }}
          >
            上架
          </Button>
        )}

        {['online'].includes(record?.egoItemStatus) && (
          <Button
            type="primary"
            text
            onClick={async () => {
              Dialog.confirm({
                title: '提示',
                content: (
                  <div
                    dangerouslySetInnerHTML={{
                      __html: `确定要下架吗？<span style="color: red;"> ${record?.title}(${record?.itemId})</span>`,
                    }}
                  />
                ),
                onOk: async () => {
                  const params: IItemOperateParams = {
                    poolId: poolBaseInfo.poolId,
                    itemIdList: [record.itemId],
                  };
                  const res = await itemOffline(params);
                  if (isTrue(res.success)) {
                    Message.success('下架成功');
                    setTimeout(() => {
                      handleSearch({ ...fields.getValues() });
                    }, 1000);
                  } else {
                    Message.error(res?.message || '下架失败');
                  }
                },
              });
            }}
            style={{ marginRight: '5px' }}
          >
            下架
          </Button>
        )}
      </div>
    );
  };

  const renderImageInfo = (value) => {
    const { imageUrl, title, itemId, itemDetailUrl } = value;
    return (
      <div className={styles.imageInfoContainer}>
        <div className={styles.imageContainer}>
          <img src={imageUrl} />
        </div>
        <div className={styles.infoContainer}>
          <div className={styles.title} title={title}>
            <a href={itemDetailUrl} target="_blank">
              {title}
            </a>
          </div>
          <span>商品id:{itemId}</span>
        </div>
      </div>
    );
  };

  const renderStatus = (value, index, record) => {
    if (!record.egoItemStatus) {
      return '';
    }
    const status = egoItemStatusOptions.find((item) => item.value === record.egoItemStatus);
    return (
      <>
      <Tag type="normal" color={status?.color} size="small">
        {status?.label}
      </Tag>
        {(record.failReason || record?.offlineReason) && (
          <Balloon.Tooltip trigger={<Icon type="help" size="small" style={{ cursor: 'pointer' }} />} align="t">
            {record.failReason || record?.offlineReason}
          </Balloon.Tooltip>
        )}
      </> 
    );
  };

  const handleSearch = async (values) => {
    if (!values.isPageChange) {
      setPagination(prev => ({
        ...prev,
        current: 1,
        pageSize: 20
      }));
    }
    setSelectedRows([]);
    setDataSource([]);
    setLoading(true);
    try {
      const params = {
        ...values,
        poolId: poolBaseInfo?.poolId,
        currentPage: values.isPageChange ? pagination.current : 1,
        pageSize: pagination.pageSize,
        queryType: 'add',
      };
      // 删除isPageChange 防止报错
      delete params?.isPageChange;
      const res: queryItemslListResult = await queryItemsList(params);
      if (isTrue(res?.success)) {
        setDataSource(res?.data?.dataList?.filter(item => item?.itemId) || []);
        setPagination((prev) => ({
          ...prev,
          total: res.data.totalCount || 0,
        }));
      } else {
        setLoading(false);
        throw new Error('接口返回数据无效');
      }
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };
  

  const handleBatchOnline = async () => {
    if (selectedRows.length === 0) {
      Dialog.alert({
        title: '提示',
        content: '请至少选择一个商品',
      });
      return;
    }

    Dialog.confirm({
      title: '提示',
      content: `确定要上架选中的 ${selectedRows.length} 个商品吗？`,
      onOk: async () => {
        const params: IItemOperateParams = {
          poolId: poolBaseInfo.poolId,
          itemIdList: selectedRows.map(item => item.itemId),
        };
        const res = await itemOnline(params);
        setSelectedRows([]);
        setTimeout(() => {
          handleSearch({ isPageChange: true });
        }, 1000);

        if (isTrue(res.success)) {
          Message.success('批量上架成功');
        } else {
          Dialog.error({ title: '提示', content: res?.message || '批量上架失败' });
        }
      },
    });
  };

  const renderTable = () => {
    return (
      <div style={{ backgroundColor: '#fff', padding: '8px', marginTop: '8px' }}>
        <div className={styles.listBtns}>
          <Button className={styles.listBtnsButton} type="primary" onClick={handleBatchOnline}>
            批量上架
          </Button>

          <Button
            className={styles.listBtnsButton}
            type="primary"
            onClick={() => {
              setActiveTab('waiting');
            }}
          >
            新增商品
          </Button>

          <Button
            className={styles.listBtnsButton}
            type="primary"
            onClick={() => {
              setIsPooledRuleVisible(true);
            }}
          >
            商品入池规则
          </Button>

          <Button
            className={styles.listBtnsButton}
            type="primary"
            onClick={() => {
              setIsOpLogDialogShow(true);
              setOpLogMode(OperationMode.PRODUCT_POOL);
            }}
          >
            操作记录
          </Button>
        </div>
        <Table
          dataSource={dataSource}
          primaryKey="itemId"
          loading={loading}
          emptyContent={
            (!poolBaseInfo?.countList?.length || poolBaseInfo?.countList?.filter(item => item?.type === 'add')?.[0]?.count > 0) && !loading?
            "暂无商品":
            dataSource?.length === 0 && poolBaseInfo?.poolId && poolBaseInfo?.countList?.length  &&
            !loading && (
              <div className={styles.emptyContent}>
                您的商品池中没有商品, 请先配置
                {
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setIsPooledRuleVisible(true);
                    }}
                  >
                    商品入池规则
                  </Button>
                }
                , 而后
                {
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setActiveTab('waiting');
                    }}
                  >
                    新增商品
                  </Button>
                }
              </div>
            )
          }
          rowSelection={{
            mode: 'multiple',
            onChange: (selectedRowKeys, records) => {
              setSelectedRows(records);
            },
            selectedRowKeys: selectedRows.map((item) => item?.itemId),
            getProps: (record) => ({
              disabled: record?.egoItemStatus === 'online',
            }),
          }}
        >
          <Table.Column
            title="商品信息"
            dataIndex="title"
            cell={(value: any, index: number, record: any) => renderImageInfo(record)}
            width={300}
            lock="left"
          />
          <Table.Column title="品牌" dataIndex="brandName" width={100} />
          <Table.Column title="叶子类目名称" dataIndex="leafCategoryName" width={120} />
          <Table.Column
            title="供应商"
            dataIndex="sellerName"
            width={100}
            cell={(value: any, index: number, record: any) => <div className={styles.ellipsisSixLine}>{value}</div>}
          />
          <Table.Column title="市场价" dataIndex="price" width={120} />
          <Table.Column title="普惠券后价" dataIndex="egoReferencePrice" width={120} />
          <Table.Column title="批发价" dataIndex="egoPrice" width={120} />
          <Table.Column title="专属价" dataIndex="platformPrice" width={120} />
          <Table.Column title="库存" dataIndex="quantity" width={80} />
          <Table.Column
            title="发货时效"
            dataIndex="shipTime"
            width={100}
            cell={(value) => {
              if (value) {
                return ` ${value}小时`;
              }
              return '';
            }}
          />
          <Table.Column
            title="商家税率"
            dataIndex="taxRate"
            width={100}
            cell={(value, index, record) => {
              return (
                <>
                  <p>{value}</p>
                  <p>{record?.taxCode}</p>
                </>
              );
            }}
          />
          <Table.Column
            title="企业购税率"
            dataIndex="egoTaxRate"
            width={120}
            cell={(value, index, record) => {
              if (value === '-1.0') {
                return '';
              }
              return (
                <>
                  <p>{value}</p>
                  <p>{record?.egoTaxCode}</p>
                </>
              );
            }}
          />
          <Table.Column
            title="操作时间"
            dataIndex="gmtModified"
            width={180}
            cell={formatTimestamp}
          />
          <Table.Column
            title="操作者"
            dataIndex="updateOperator"
            width={120}
          />
          <Table.Column title="状态" dataIndex="egoItemStatus" cell={renderStatus} width={120} lock="right" />
          <Table.Column title="操作" cell={renderActions} width={200} lock="right" />
        </Table>
        <div>
          <Pagination
            className={styles.myPagination}
            type="normal"
            shape="normal"
            showJump
            pageSizeSelector="dropdown"
            pageSizeList={[10, 20]}
            defaultCurrent={1}
            {...pagination}
            totalRender={(total) => `共 ${total} 条`}
            onPageSizeChange={(pageSize) => {
              setPagination((prev) => ({
                ...prev,
                pageSize,
              }));
            }}
            onChange={(current) => {
              setPagination((prev) => ({
                ...prev,
                current,
              }));
            }}
          />
        </div>
      </div>
    );
  };

  useEffect(() => {
    if (poolBaseInfo?.poolId) {
      handleSearch({ isPageChange: true, ...fields.getValues() });
      // Reset selected rows when page changes
      setSelectedRows([]);
    }
  }, [poolBaseInfo?.poolId, pagination.current, pagination.pageSize]);

  const handleSkuDetailUpdate = (updatedItemData: Product) => {
    setDataSource((prevDataSource: Product[]) =>
      prevDataSource.map((item) => (item.itemId === updatedItemData.itemId ? updatedItemData : item)),
    );
  };
  return (
    <div className={styles.pooledProductList}>
      <SearchForm onSearch={handleSearch} fields={fields} poolId={poolBaseInfo?.poolId} />
      {renderTable()}
      <SkuDetail
        mode="edit"
        // 增加已入池标识，组件点击确定后，调用编辑接口,没有此标识则不调用
        poolType="add"
        visible={isSkuDetailVisible}
        onClose={() => setIsSkuDetailVisible(false)}
        currentItemData={currentRecordRef.current}
        onUpdate={handleSkuDetailUpdate}
      />
      <OperationRecord
        poolId={poolBaseInfo?.poolId}
        mode={opLogMode as OperationMode}
        visible={isOpLogDialogShow}
        onClose={() => setIsOpLogDialogShow(false)}
      />
      <ProductPoolRule
        poolBaseInfo={poolBaseInfo}
        visible={isPooledRuleVisible}
        onCancel={() => setIsPooledRuleVisible(false)}
      />
    </div>
  );
};

export default PooledProductList;
