import React, { useState } from 'react';
import { Input, Select, Button, Grid, Form, Field, NumberPicker } from '@alifd/next';
import CategoryTreeSelect from '@/components/CategoryTreeSelect';
import BrandSelect from '@/components/BrandSelect';
import { egoItemStatusOptions } from '@/pages/Utils';

import styles from './index.module.scss';

interface SearchFormProps {
  poolId: number;
  fields: Field;
  onSearch: (values: any) => void;
}

const SearchForm: React.FC<SearchFormProps> = ({ poolId, onSearch, fields }) => {

  const formItemLayout = {
    labelCol: {
      fixedSpan: 8,
    },
    wrapperCol: {
      span: 16,
    },
  };

  return (
    <Form field={fields} labelAlign="top" {...formItemLayout} style={{ backgroundColor: '#fff', padding: '8px' }}>
      <Grid.Row gutter="10">
        <Grid.Col>
          <Form.Item name="title" label="商品名称">
            <Input placeholder="请输入商品名称" />
          </Form.Item>
        </Grid.Col>
        <Grid.Col>
          <Form.Item name="itemId" label="商品ID">
            <NumberPicker placeholder="请输入商品ID" hasTrigger={false} style={{ width: '100%' }} />
          </Form.Item>
        </Grid.Col>
        <Grid.Col>
          <Form.Item name="egoItemStatus" label="上下架状态">
            <Select placeholder="请选择" dataSource={egoItemStatusOptions} style={{ width: '100%' }} hasClear followTrigger />
          </Form.Item>
        </Grid.Col>
        <Grid.Col>
          <Form.Item name="leafCategoryNameList" label="叶子类目名称">
            <CategoryTreeSelect
              mode="multiple"
              isFull={false}
              poolId={poolId}
              onChange={(value, option) => {
                // const labels = value.map((opt) => opt).join(', ');
                fields.setValue('leafCategoryData', value);
              }}
            />
          </Form.Item>
        </Grid.Col>
        <Grid.Col>
          <Form.Item name="brandNameList" label="品牌">
            <BrandSelect mode="multiple" isLimit={true} poolId={poolId} />
          </Form.Item>
        </Grid.Col>
        <Grid.Col>
          <Form.Item label={' '}>
            <div className={styles.rcodeSearchBtns}>
              <Button
                className={styles.searchBtnsButton}
                type="primary"
                onClick={() => {
                  const values = fields.getValues();
                  if (values.leafCategoryData) {
                    values.leafCategoryNameList = values.leafCategoryData;
                    delete values.leafCategoryData;
                  }
                  if(values.itemId){
                    values.itemId = String(values.itemId);
                  }
                  onSearch({ queryType: 'add', isPageChange: false, ...values });
                }}
              >
                查询
              </Button>

              <Button
                className={styles.searchBtnsButton}
                type="normal"
                onClick={() => {
                  fields.resetToDefault();
                  onSearch({ queryType: 'add', isPageChange: false });
                }}
              >
                重置
              </Button>
            </div>
          </Form.Item>
        </Grid.Col>
      </Grid.Row>
    </Form>
  );
};

export default SearchForm;
