import React, { useState, useEffect, useRef } from 'react';
import { Table, Button, Pagination, Divider, Dialog, Message, Field } from '@alifd/next';
import SkuDetail from '@/components/ItemPoolComp/skuDetail';
// @ts-ignore
import PendingPoolListFilter from './pendingPoolListFilter';
import { queryItemsList, itemOperate } from '@/services';
import { IQueryItemsListParams, queryItemslListResult, Product, IItemOperateParams } from '@/types';

import styles from './index.module.scss';
import { isTrue } from '@/pages/Utils';

const PendingPoolList = ({ poolBaseInfo }) => {
  const currentRecordRef = useRef<Product | null>(null);
  const [isSkuDetailVisible, setIsSkuDetailVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<Product[]>([]);
  const [pagination, setPagination] = useState({ current: 1, pageSize: 20, total: 0 });
  const [selectedRecords, setSelectedRecords] = useState<Product[]>([]);
  const fields = Field.useField();

  const renderActions = (value: any, index: number, record: Product) => {
    return (
      <div className={styles.actions}>
        {!record?.skuList?.length ||  record?.skuList?.length === 1 ? null : (
          <Button
            type="primary"
            text
            onClick={() => {
              currentRecordRef.current = {
                poolId: poolBaseInfo?.poolId,
                ...record,
              };
              setIsSkuDetailVisible(true);
            }}
            style={{ marginRight: '10px' }}
          >
            选择sku
          </Button>
        )}
        <Button 
          text 
          type="primary"
          onClick={() => {
            Dialog.confirm({
              title: '确认',
              content: '确认将该商品加入商品池？',
              onOk: async () => {
                const params: IItemOperateParams = {
                  poolId: poolBaseInfo.poolId,
                  itemList: [{
                    itemId: record.itemId,
                    ...(record.skuList?.length ? { } : { ratioPrice: 0 }),
                    skuList: record?.skuList?.map((sku) => ({
                      skuId: sku.skuId,
                      ratioPrice: 0,
                      enable: sku?.enable ?? true,
                    })),
                  }],
                };
                const res = await itemOperate(params);
                if (isTrue(res.success)) {
                  Message.success('加入商品池成功');
                  setTimeout(() => {
                    poolBaseInfo?.fetchPoolBaseInfo();
                    handleSearch({});
                  }, 1000);
                } else {
                  Message.error(res?.message ?? '加入商品池失败');
                }
              }
            });
          }}
        >
          添加
        </Button>
      </div>
    );
  };

  const renderImageInfo = (value: {
    imageUrl: string;
    title: string;
    itemId: string | number;
    itemDetailUrl: string;
  }) => {
    const { imageUrl, title, itemId, itemDetailUrl } = value;
    return (
      <div className={styles.imageInfoContainer}>
        <div className={styles.imageContainer}>
          <img src={imageUrl} />
        </div>

        <div className={styles.infoContainer}>
          <div className={styles.title}>
            <a href={itemDetailUrl} target="_blank">
              {title}
            </a>
          </div>
          <span>商品id:{itemId}</span>
        </div>
      </div>
    );
  };

  const handleSearch = async (values: Partial<IQueryItemsListParams>, resetPage?: boolean) => {
    setLoading(true);
    try {
      const params: IQueryItemsListParams = {
        ...values,
        currentPage: resetPage ? 1 : pagination.current,
        pageSize: pagination.pageSize,
        queryType: 'waiting',
        poolId: poolBaseInfo.poolId,
      };
      const res: queryItemslListResult = await queryItemsList(params);
      setDataSource((res?.data?.dataList || []).filter(Boolean));
      setPagination((prev) => ({
        ...prev,
        total: res?.data?.totalCount || 0,
      }));
      if (resetPage) {
        setSelectedRecords([]);
        setPagination((prev) => ({ ...prev, current: 1 }));
      }
    } catch (error) {
      console.error('查询失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const renderTable = () => {
    return (
      <div className={styles.cardFilterResults}>
        <div style={{ display: 'flex', alignItems: 'center', position: 'relative' }}>
          <span className={styles.cardTitle}>筛选结果</span>
          <div style={{ marginLeft: '16px' }}>
            共{pagination?.total}个商品，已选择{selectedRecords.length}个商品
          </div>
          <Button
            className={styles.buttonAddToPool}
            type="primary"
            onClick={() => {
              if (selectedRecords.length === 0) {
                Dialog.warning({ title: '提示', content: '请选择至少一个商品。' });
                return;
              }
              Dialog.confirm({
                title: '确认',
                content: `确认将选中的 ${selectedRecords.length} 个商品加入商品池？`,
                onOk: async () => {
                  const params: IItemOperateParams = {
                    poolId: poolBaseInfo.poolId,
                    itemList: selectedRecords.map((item) => ({
                      itemId: item.itemId,
                      ...(item.skuList?.length ? { } : { ratioPrice: 0 }),
                      skuList: item?.skuList?.map((sku) => ({
                        skuId: sku.skuId,
                        ratioPrice: 0,
                        enable: sku?.enable ?? true,
                      })),
                    })),
                  };
                  const res = await itemOperate(params);
                  if (isTrue(res.success)) {
                    Message.success('加入商品池成功');
                    setTimeout(() => {
                      poolBaseInfo?.fetchPoolBaseInfo();
                      handleSearch({}, true);
                    }, 10000);
                  } else {
                    Message.error(res?.message ?? '加入商品池失败');
                  }
                }
              });
            }}
          >
            加入商品池
          </Button>
        </div>
        <Divider />
        <div className={styles.cardContainer}>
          <Table
            dataSource={dataSource}
            primaryKey="itemId"
            loading={loading}
            rowSelection={{
              mode: 'multiple',
              onChange: (v, records) => {
                setSelectedRecords(records);
              },
            }}
            emptyContent={<p>暂无商品</p>}
          >
            <Table.Column
              title="商品信息"
              dataIndex="imageUrl"
              cell={(value: any, index: number, record: any) => renderImageInfo(record)}
              width={300}
              lock="left"
            />
            <Table.Column title="品牌" dataIndex="brandName" width={100} />
            <Table.Column title="叶子类目名称" dataIndex="leafCategoryName" width={120} />
            <Table.Column
              title="供应商"
              dataIndex="sellerName"
              width={100}
              cell={(value: any, index: number, record: any) => <div className={styles.ellipsisSixLine}>{value}</div>}
            />
            <Table.Column title="市场价" dataIndex="price" width={140} />
            <Table.Column title="普惠券后价" dataIndex="egoReferencePrice" width={140} />
            <Table.Column title="批发价" dataIndex="egoPrice" width={140} />
            <Table.Column title="库存" dataIndex="quantity" width={100} />
            <Table.Column
              title="发货时效"
              dataIndex="shipTime"
              width={100}
              cell={(value) => {
                if (value) {
                  return ` ${value}小时`;
                }
                return '';
              }}
            />
            <Table.Column
              title="商家税率税编"
              dataIndex="taxRate"
              width={150}
              cell={(value, index, record) => {
                return (
                  <>
                    <p>{value}</p>
                    <p>{record?.taxCode}</p>
                  </>
                );
              }}
            />
            <Table.Column
              title="企业购税率税编"
              dataIndex="egoTaxRate"
              width={150}
              cell={(value, index, record) => {
                return (
                  <>
                    <p>{value}</p>
                    <p>{record?.egoTaxCode}</p>
                  </>
                );
              }}
            />
            <Table.Column
              title="已选sku/总sku数"
              dataIndex="operateList"
              width={130}
              cell={(value, index, record) => {
                return `${record?.skuList?.filter((sku) => sku.enable || sku.enable === undefined)?.length || 0}/${
                  record?.skuList?.length || 0
                }`;
              }}
            />
            <Table.Column title="操作" cell={renderActions} width={120} lock="right" />
          </Table>
          <div>
            <Pagination
              style={{ margin: '10px 0' }}
              className={styles.myPagination}
              type="normal"
              shape="normal"
              showJump
              pageSizeSelector="dropdown"
              pageSizeList={[10, 20]}
              defaultCurrent={1}
              current={pagination?.current}
              pageSize={pagination?.pageSize}
              total={pagination?.total}
              onPageSizeChange={(pageSize) => {
                setPagination((prev) => ({
                  ...prev,
                  pageSize,
                }));
              }}
              onChange={(current) => {
                setPagination((prev) => ({
                  ...prev,
                  current,
                }));
              }}
              totalRender={(total) => `共 ${total} 条`}
            />
          </div>
        </div>
      </div>
    );
  };

  useEffect(() => {
    const values = fields.getValues();
    if (values.leafCategoryData) {
      values.leafCategoryIdList = values?.leafCategoryData?.map((item) => item?.value) || [];
      delete values.leafCategoryData;
      delete values.leafCategoryNameList;
    }

    // 处理商品ID集合
    if (values.itemIdListInput && typeof values.itemIdListInput === 'string') {
      const itemIds = values.itemIdListInput
        .split(/[,，\s]+/)
        .map((id: string) => id.trim())
        .filter((id: string) => id && /^\d+$/.test(id))
        .map((id: string) => Number(id));
      values.itemIdList = itemIds;
      delete values.itemIdListInput;
    }

    // 处理卖家ID集合
    if (values.sellerIdListInput && typeof values.sellerIdListInput === 'string') {
      const sellerIds = values.sellerIdListInput
        .split(/[,，\s]+/)
        .map((id: string) => id.trim())
        .filter((id: string) => id && /^\d+$/.test(id))
        .map((id: string) => Number(id));
      values.sellerIdList = sellerIds;
      delete values.sellerIdListInput;
    }

    handleSearch({ ...values });
    // Reset selected rows when page changes
    setSelectedRecords([]);
  }, [poolBaseInfo?.poolId, pagination.current, pagination.pageSize]);

  const handleSkuDetailUpdate = (updatedItemData: Product) => {
    setDataSource((prevDataSource: Product[]) =>
      prevDataSource.map((item) => (item.itemId === updatedItemData.itemId ? updatedItemData : item)),
    );
  };

  return (
    <div className={styles.pendingPoolListContainer}>
      <PendingPoolListFilter poolBaseInfo={poolBaseInfo} onSearch={handleSearch} fields={fields} />
      {renderTable()}
      <SkuDetail
        onUpdate={handleSkuDetailUpdate}
        itemId={currentRecordRef.current?.itemId}
        currentItemData={currentRecordRef.current as Product}
        mode="edit"
        visible={isSkuDetailVisible}
        onClose={() => setIsSkuDetailVisible(false)}
        poolType='pending'
      />
    </div>
  );
};

export default PendingPoolList;
