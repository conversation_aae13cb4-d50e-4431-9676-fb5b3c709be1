import React, { useState } from 'react';
import { <PERSON>, Divider, Button, NumberPicker, Input } from '@alifd/next';
import CategoryTreeSelect from '@/components/CategoryTreeSelect';
import BrandSelect from '@/components/BrandSelect';
import ProductPoolRule from '../PooledProductList/productPoolRule';

import styles from './index.module.scss';

const PendingPoolListFilter = ({ poolBaseInfo, onSearch, fields }) => {
  const [isPooledRuleVisible, setIsPooledRuleVisible] = useState(false);

  const handleSearch = (values, errors) => {
    if (!errors && onSearch) {
      if (values.leafCategoryData) {
        values.leafCategoryIdList = values?.leafCategoryData?.map((item) => item?.value) || [];
        delete values.leafCategoryData;
        delete values.leafCategoryNameList;
      }

      // 处理商品ID集合
      if (values.itemIdListInput) {
        const itemIds = values.itemIdListInput
          .split(/[,，\s]+/)
          .map(id => id.trim())
          .filter(id => id && /^\d+$/.test(id))
          .map(id => Number(id));
        values.itemIdList = itemIds;
        delete values.itemIdListInput;
      }

      // 处理卖家ID集合
      if (values.sellerIdListInput) {
        const sellerIds = values.sellerIdListInput
          .split(/[,，\s]+/)
          .map(id => id.trim())
          .filter(id => id && /^\d+$/.test(id))
          .map(id => Number(id));
        values.sellerIdList = sellerIds;
        delete values.sellerIdListInput;
      }

      onSearch({ queryType: 'wating', ...values }, true);
    }
  };

  const renderMyForm = () => {
    const formItemLayout = {
      labelCol: {
        fixedSpan: 4,
        style: {
          textAlign: 'left',
          paddingRight: '6px',
        },
      },
      wrapperCol: {
        span: 18,
      },
    };

    return (
      <Form field={fields} {...formItemLayout} labelAlign="left">
        <Form.Item name="itemIdListInput" label="商品ID">
          <Input
            style={{ width: '400px' }}
            placeholder="请输入商品ID，多个ID用逗号分隔，如：123456,789012"
            hasClear
          />
        </Form.Item>
        <Form.Item name="sellerIdListInput" label="卖家ID">
          <Input
            style={{ width: '400px' }}
            placeholder="请输入卖家ID，多个ID用逗号分隔，如：123456,789012"
            hasClear
          />
        </Form.Item>
        <Form.Item name="sellerName" label="商家名称">
          <Input
            style={{ width: '400px' }}
            placeholder="请输入商家名称"
            hasClear
          />
        </Form.Item>
        <Form.Item name="leafCategoryNameList" label="叶子类目名称">
          <CategoryTreeSelect
            isTree={true}
            mode="multiple"
            style={{ width: '400px' }}
            poolId={poolBaseInfo?.poolId}
            onChange={(value, option) => {
              fields.setValue('leafCategoryData', option);
            }}
          />
        </Form.Item>
        <Form.Item name="brandNameList" label="品牌名称">
          <BrandSelect mode="multiple" isAll={true} style={{ width: '400px' }} poolId={poolBaseInfo?.poolId} />
        </Form.Item>
        <Form.Item label="价格区间" style={{ marginBottom: 0 }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '5px' }}>
            <Form.Item name="minEgoPrice">
              <NumberPicker
                style={{ width: '164px' }}
                placeholder="最小值"
                innerAfter="元"
                step={0.01}
                min={0}
                max={fields?.getValue('maxEgoPrice')}
              />
            </Form.Item>
            <span className={styles.priceRangeLabel}> ≤ 批发价 ≤ </span>
            <Form.Item name="maxEgoPrice">
              <NumberPicker
                style={{ width: '164px' }}
                placeholder="最大值"
                innerAfter="元"
                step={0.01}
                min={fields?.getValue('minEgoPrice') ?? 0}
              />
            </Form.Item>
          </div>
        </Form.Item>
        <Form.Item name="minQuantity" label="库存 >=">
          <NumberPicker style={{ width: '400px' }} placeholder="请输入" innerAfter="件" min={1} />
        </Form.Item>
        <Form.Item>
          <Form.Submit type="primary" validate onClick={handleSearch} style={{ marginRight: '10px' }}>
            筛选
          </Form.Submit>
          <Form.Reset
            onClick={() => {
              fields.setValue('leafCategoryData', []);
              fields.setValue('itemIdListInput', '');
              fields.setValue('sellerIdListInput', '');
              fields.setValue('sellerName', '');
              fields.resetToDefault();
              onSearch({ queryType: 'wating' }, true);
            }}
            style={{ marginRight: '10px' }}
          >
            重置
          </Form.Reset>
        </Form.Item>
      </Form>
    );
  };

  return (
    <div className={styles.pendingPoolListFilter}>
      <div className={styles.title}>商品筛选规则填写</div>
      <Divider />
      <div className={styles.basicPooledRule}>
        {poolBaseInfo?.poolRule?.discountRateReference !== undefined || poolBaseInfo?.poolRule?.discountRateOrigin !== undefined || poolBaseInfo?.poolRule?.shipTime !== undefined ? (
          <>
            <p>基础入池规则: (所有商品筛选结果会叠加此条件)</p>
            {(poolBaseInfo?.poolRule?.discountRateReference !== undefined || poolBaseInfo?.poolRule?.discountRateOrigin !== undefined) && (
              <p>
                价格力:
                {poolBaseInfo?.poolRule?.discountRateReference !== undefined && 
                  `(批发价/普惠券后价)折扣率小于等于${(poolBaseInfo?.poolRule?.discountRateReference * 10)?.toFixed(1) ?? " -- "} 折;`
                }
                &nbsp; &nbsp;
                {poolBaseInfo?.poolRule?.discountRateOrigin !== undefined &&
                  `(批发价/市场价)折扣率小于等于${(poolBaseInfo?.poolRule?.discountRateOrigin * 10)?.toFixed(1) ?? " -- "} 折;`
                }
              </p>
            )}
            {poolBaseInfo?.poolRule?.shipTime !== undefined && (
              <p>服务&履约能力: 发货时效小于等于 {poolBaseInfo?.poolRule?.shipTime ?? " -- "}小时; </p>
            )}
            <Button className={styles.updateBtn} text type="primary" onClick={() => setIsPooledRuleVisible(true)}>
              修改
            </Button>
          </>
        ) : (
          <Button type="primary" text onClick={() => setIsPooledRuleVisible(true)}>添加基础入池规则</Button>
        )}
      </div>
      <div style={{ padding: '4px' }}>{renderMyForm()}</div>
      <ProductPoolRule
        poolBaseInfo={poolBaseInfo}
        visible={isPooledRuleVisible}
        onCancel={() => setIsPooledRuleVisible(false)}
        onSuccess={() => onSearch({ queryType: 'wating'}, true)}
      />
    </div>
  );
};

export default PendingPoolListFilter;
