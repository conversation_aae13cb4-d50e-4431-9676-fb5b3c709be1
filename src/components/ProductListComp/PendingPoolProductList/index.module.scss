.pendingPoolListContainer {
  padding: 10px;
  min-height: 100vh;
  .imageInfoContainer {
    display: flex;
    .imageContainer {
      width: 96px;
      height: 96px;
      border-radius: 6px;
      img {
        width: 96px;
        height: 96px;
        border-radius: 6px;
      }
    }
    .infoContainer {
      margin-left: 13px;
      .title {
        height: 44px;
        font-family: PingFangSC;
        font-weight: 500;
        font-size: 14px;
        color: #4f89ff;
        letter-spacing: 0;
        line-height: 22px;
        margin-bottom: 16px;
        // 最多两行超出省略
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        overflow: hidden;
        width: 158px;
      }
      span {
        font-family: PingFangSC;
        font-weight: 400;
        font-size: 14px;
        color: #666666;
        letter-spacing: 0;
        line-height: 22px;
      }
    }
  }

  .myPagination {
    margin-top: 10px;
    display: flex;
    flex-grow: 1;
    justify-content: flex-end;
    .nextPaginationPages {
      margin-left: auto;
    }
  }

  .cardFilterResults {
    background-color: #fff;
    border: 1px solid #e8e8e8;
    border-radius: 4px;
    padding: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin: 10px 0;

    .cardTitle {
      color: #111;
      font-size: 18px;
      font-weight: 500;
      line-height: 26px;
    }
  }

  .buttonAddToPool {
    position: absolute;
    right: 0;
  }

  .pendingPoolListFilter {
    background-color: #fff;
    padding: 8px;
    .priceRangeLabel {
      color: #333;
      font-size: 12px;
      line-height: 28px;
      height: 38px;
    }
    .basicPooledRule {
      background: #f3f2f2cc;
      padding: 8px;
      border-radius: 8px;
      position: relative;
      margin-bottom: 12px;
      .updateBtn {
        position: absolute;
        right: 8px;
        bottom: 10px;
      }
    }
  }
  .ellipsisSixLine {
    display: -webkit-box;
    -webkit-line-clamp: 6;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}
