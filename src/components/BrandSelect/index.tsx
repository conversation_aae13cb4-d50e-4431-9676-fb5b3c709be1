import React, { useState, useEffect } from 'react';
import { Select } from '@alifd/next';
import { queryBrand, queryBrandLimit,  queryBrandAll } from '@/services';
import { IQueryBrandParams, queryBrandResult } from '@/types';
interface BrandOption {
  label: string;
  value: string;
}

interface BrandSelectProps {
  value?: string[];
  onChange?: (value: string[]) => void;
  style?: React.CSSProperties;
  mode?: 'single' | 'multiple';
  hasClear?: boolean;
  isAll?: boolean;
  poolId?: number;
  isLimit?: boolean;
}

const BrandSelect: React.FC<BrandSelectProps> = ({ value, onChange, mode = 'single', style = { width: '100%' }, hasClear = true,  isAll = false, poolId, isLimit = false  }) => {
  const [brandOptions, setBrandOptions] = useState<BrandOption[]>([]);

  const fetchBrandList = async (searchValue: string) => {
    try {
      const response = isLimit 
        ? await queryBrandLimit({ brandName: searchValue, poolId: poolId })
        : await queryBrand({ brandName: searchValue });
      // 转换数据格式
      const transformedData =
        response?.data?.map((item) => ({
          value: item,
          label: item,
        })) || [];
      setBrandOptions(transformedData);
    } catch (error) {
      console.error('获取品牌列表失败:', error);
    }
  };

  const fetchBrandAllList = async () => {
    const response = await queryBrandAll({ poolId: poolId });
    const transformedData =
        response?.data?.map((item) => ({
          value: item,
          label: item,
        })) || [];
      setBrandOptions(transformedData);
  };

  useEffect(() => {
    if (isAll && poolId) {
      fetchBrandAllList();
    }
  }, [isAll, poolId]);

  return (
    <Select
      hasClear={hasClear}
      showSearch
      followTrigger={true}
      placeholder="请输入品牌名称搜索"
      style={{ ...style }}
      dataSource={brandOptions as any}
      onSearch={(value) => {
        if (value && !isAll) {
          fetchBrandList(value);
        }
      }}
      mode={mode}
      filterLocal={false}
      value={value}
      onChange={onChange}
      maxTagCount={1}
    />
  );
};

export default BrandSelect;
