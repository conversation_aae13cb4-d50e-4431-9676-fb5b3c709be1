import React from 'react';
import { Tag } from '@alifd/next';

export enum Status {
  PENDING = 0,
  VALID = 1,
  INVALID = -1
}

interface StatusConfig {
  color: string;
  text: string;
}

const STATUS_CONFIG: Record<Status, StatusConfig> = {
  [Status.PENDING]: {
    color: 'gold',
    text: '待生效'
  },
  [Status.VALID]: {
    color: 'green',
    text: '已生效'
  },
  [Status.INVALID]: {
    color: 'red',
    text: '已失效'
  }
};

// Add a mapping from string to enum value
const STATUS_STRING_MAP: Record<string, Status> = {
  'PENDING': Status.PENDING,
  'VALID': Status.VALID,
  'INVALID': Status.INVALID,
};

interface StatusTagProps {
  status: Status | keyof typeof STATUS_STRING_MAP;  // Update type to accept string
  className?: string;
}

const ListStatusTag: React.FC<StatusTagProps> = ({ status, className }) => {
  // Convert string status to enum value if needed
  const statusValue = typeof status === 'string' ? STATUS_STRING_MAP[status] : status;
  const config = STATUS_CONFIG[statusValue];
  
  if (!config) {
    return null;
  }

  return (
    <Tag color={config.color} className={className} size="small">
      {config.text}
    </Tag>
  );
};

export default ListStatusTag;