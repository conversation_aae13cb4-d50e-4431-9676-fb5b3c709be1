.tableContainer {
  width: 100%;
  position: relative;

  &:global(.scrollable) {
    overflow-x: auto;
  }

  .adaptiveTable {
    width: 100%;

    :global {
      .next-table-body {
        overflow-x: auto !important;
      }

      .next-table-header {
        overflow-x: hidden !important;
      }

      .next-table-inner {
        width: 100%;
      }

      // 确保表格在固定布局模式下正确显示
      .next-table-fixed {
        table-layout: fixed;
      }

      // 确保表格在自动布局模式下正确显示
      .next-table-auto {
        table-layout: auto;
      }

      // 锁定列样式
      .next-table-lock-left {
        box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
        z-index: 2;
      }

      .next-table-lock-right {
        box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
        z-index: 2;
      }

      // 锁定列单元格样式
      .next-table-cell.next-table-cell.next-table-cell-fix-left,
      .next-table-cell.next-table-cell.next-table-cell-fix-right {
        background-color: #fff;
      }

      // 锁定列表头样式
      .next-table-header .next-table-cell.next-table-cell-fix-left,
      .next-table-header .next-table-cell.next-table-cell-fix-right {
        background-color: #f5f5f5;
      }
    }
  }
}
