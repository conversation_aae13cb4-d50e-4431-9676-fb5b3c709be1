import React, { useState, useEffect, useRef } from 'react';
import { Table } from '@alifd/next';
import type { TableProps } from '@alifd/next/types/table';
import styles from './index.module.scss';

interface OptimizedAdaptiveTableProps extends TableProps {
  columns: any[]; // 确保有columns属性
}

// 节流函数，限制函数调用频率
const throttle = (fn: Function, delay: number) => {
  let lastCall = 0;
  return (...args: any[]) => {
    const now = new Date().getTime();
    if (now - lastCall < delay) {
      return;
    }
    lastCall = now;
    return fn(...args);
  };
};

// 计算表格内容的总宽度
const calculateTableContentWidth = (columns: any[]): number => {
  return columns.reduce((total, column) => {
    // 如果列有明确的宽度，使用它
    if (column.width) {
      return total + (typeof column.width === 'number' ? column.width : parseInt(column.width, 10) || 100);
    }
    // 如果没有明确的宽度，使用默认值
    return total + 100; // 默认列宽
  }, 0);
};

const OptimizedAdaptiveTable: React.FC<OptimizedAdaptiveTableProps> = ({
  columns,
  ...tableProps
}) => {
  const [useFixedLayout, setUseFixedLayout] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);
  const contentWidthRef = useRef<number>(calculateTableContentWidth(columns));

  useEffect(() => {
    // 更新内容宽度引用
    contentWidthRef.current = calculateTableContentWidth(columns);

    // 检查是否需要启用固定布局和滚动
    const checkNeedScroll = throttle(() => {
      if (containerRef.current) {
        const containerWidth = containerRef.current.clientWidth;
        const contentWidth = contentWidthRef.current;

        // 如果内容宽度大于容器宽度，启用固定布局和滚动
        setUseFixedLayout(contentWidth > containerWidth);
      }
    }, 100); // 100ms的节流时间

    // 初始调用一次以获取正确的初始状态
    checkNeedScroll();

    window.addEventListener('resize', checkNeedScroll);
    return () => {
      window.removeEventListener('resize', checkNeedScroll);
    };
  }, [columns]); // 当columns变化时重新计算

  // 检查是否有锁定列
  const hasLockColumns = columns.some(col => col.lock === 'left' || col.lock === 'right');

  return (
    <div
      ref={containerRef}
      className={`${styles.tableContainer} ${useFixedLayout ? 'scrollable' : ''}`}
    >
      <Table
        {...tableProps}
        columns={columns}
        tableLayout={useFixedLayout ? 'fixed' : 'auto'}
        className={styles.adaptiveTable}
        // 当有锁定列且需要滚动时，启用锁定列功能
        fixedHeader={hasLockColumns && useFixedLayout}
        // 确保表格内容可以滚动
        maxBodyHeight={hasLockColumns && useFixedLayout ? 'calc(100vh - 300px)' : undefined}
        // 设置锁定列的阴影效果
        stickyLock={hasLockColumns && useFixedLayout}
      />
    </div>
  );
};

export default OptimizedAdaptiveTable;
