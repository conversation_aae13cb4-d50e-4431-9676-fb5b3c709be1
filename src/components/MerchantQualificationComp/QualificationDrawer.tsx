import React, { useState, useEffect } from 'react';
import { Drawer, Form, Field, Input, Message, Loading, Dialog, Button } from '@alifd/next';
import { queryQualificationDetail } from '@/services/merchantQualification';
import { QualificationItem } from '@/types/merchantQualification';
import { formatTimestamp } from '@/pages/Utils';
import styles from './index.module.scss';
import AuditDialog from './AuditDialog';
import { auditAll } from '@/services';

interface QualificationDrawerProps {
  visible: boolean;
  applyId?: string;
  onClose: () => void;
  onRefresh?: () => void;
}

const QualificationDrawer: React.FC<QualificationDrawerProps> = ({
  visible,
  applyId,
  onClose,
  onRefresh,
}) => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<QualificationItem | null>(null);
  const field = Field.useField();
  const [previewVisible, setPreviewVisible] = useState(false);
  const [currentImage, setCurrentImage] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [auditDialogVisible, setAuditDialogVisible] = useState(false);

  const fetchDetail = async () => {
    if (!applyId) return;
    
    setLoading(true);
    try {
      const response = await queryQualificationDetail(applyId);
      if (response.data) {
        setData(response.data);
        field.setValues(response.data);
      }
    } catch (error) {
      console.error('获取资质详情失败:', error);
      Message.error('获取资质详情失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible && applyId) {
      setData(null); // 清空上一次的数据
      fetchDetail();
    }
  }, [visible, applyId]);

  const renderCategoryList = () => {
    if (!data?.licenseCategoryList || data.licenseCategoryList.length === 0) {
      return null;
    }

    return (
      <table style={{ width: '100%', marginTop: '8px', borderCollapse: 'collapse' }}>
        <thead>
          <tr>
            <th style={tableHeaderStyle}>类目路径</th>
            <th style={tableHeaderStyle}>叶子类目</th>
          </tr>
        </thead>
        <tbody>
          {data.licenseCategoryList.map((category, index) => (
            <tr key={index}>
              <td style={tableCellStyle}>{category.path?.join('->') || ''}</td>
              <td style={tableCellStyle}>{category.name}</td>
            </tr>
          ))}
        </tbody>
      </table>
    );
  };

  const handlePreview = (url: string, index: number) => {
    setCurrentImage(url);
    setCurrentIndex(index);
    setPreviewVisible(true);
  };

  const handlePrev = () => {
    if (!data?.licenseUrlList) return;
    const newIndex = (currentIndex - 1 + data.licenseUrlList.length) % data.licenseUrlList.length;
    setCurrentIndex(newIndex);
    setCurrentImage(data.licenseUrlList[newIndex]);
  };

  const handleNext = () => {
    if (!data?.licenseUrlList) return;
    const newIndex = (currentIndex + 1) % data.licenseUrlList.length;
    setCurrentIndex(newIndex);
    setCurrentImage(data.licenseUrlList[newIndex]);
  };

  const handleAudit = () => {
    setAuditDialogVisible(true);
  };

  const handleAuditSubmit = async (values: any) => {
    try {
      if (!data) {
        Message.error('当前记录不存在');
        return;
      }

      const result = await auditAll({
        instanceType: 'bpms',
        targetType: 'brand_licence',
        auditType: values.auditStatus === 'APPROVED' ? 'approve' : 'reject',
        targetIdList: [String(data.id)],
        mark: values.auditStatus === 'REJECTED' ? values.rejectReason : ''
      });

      if (result.success) {
        Message.success('审核操作成功');
        setAuditDialogVisible(false);
        // 延迟刷新列表和统计数据
        setTimeout(() => {
          onRefresh?.();
          onClose();
        }, 1000);
      } else {
        Message.error(result?.message || '审核操作失败');
      }
    } catch (error) {
      console.error('审核操作失败:', error);
      Message.error('审核操作失败');
    }
  };

  return (
    <>
      <Drawer
        title="查看品牌授权"
        visible={visible}
        width={600}
        onClose={onClose}
      >
        <Loading visible={loading} style={{ width: '100%' }}>
          <Form 
            field={field} 
            labelCol={{ span: 6 }} 
            wrapperCol={{ span: 14 }} 
            isPreview
            className={styles.qualificationForm}
          >
            <Form.Item label="申请id" labelAlign="left" colon>
              <Input name="id" />
            </Form.Item>
            <Form.Item label="商标注册号" labelAlign="left" colon>
              <Input name="registerId" />
            </Form.Item>
            <Form.Item label="品牌名称" labelAlign="left" colon>
              <Input name="brandName" />
            </Form.Item>
            <Form.Item label="品牌logo" labelAlign="left" colon>
              {data?.brandLogoUrl && (
                <img 
                  src={data.brandLogoUrl} 
                  alt="品牌logo" 
                  style={{ maxWidth: '100px', maxHeight: '100px' }}
                />
              )}
            </Form.Item>
            <Form.Item label="品牌所有人" labelAlign="left" colon>
              <Input name="brandOwner" />
            </Form.Item>
            <Form.Item label="品牌授权书" labelAlign="left" colon>
              <div style={{ display: 'flex', flexDirection: 'column', marginTop: 4 }}>
                <div>
                  {data?.licenseUrlList && data.licenseUrlList.length > 0 && (
                    <div style={{ display: 'flex', flexWrap: 'wrap', gap: '10px' }}>
                      {data.licenseUrlList.map((url, index) => (
                        <img 
                          key={index}
                          src={url} 
                          alt={`授权书${index + 1}`}
                          style={{ maxWidth: '100px', maxHeight: '100px', cursor: 'pointer' }}
                          onClick={() => handlePreview(url, index)}
                        />
                      ))}
                    </div>
                  )}
                </div>
                <div style={{ fontSize: '12px', marginTop: '8px' }}>
                  {data?.licenseCheckResult?.tip && (
                    <div style={{ color: 'red', marginBottom: '4px' }}>风险提示：</div>
                  )}
                  {data?.licenseCheckResult?.tip?.split('。').map((sentence, index) => 
                    sentence ? (
                      <div key={index} style={{ marginTop: '4px' }}>
                        {sentence.includes('系统检测到') ? (
                          <>
                            <span style={{ color: 'black' }}>
                              系统检测到
                            </span>
                            <span style={{ color: 'red' }}>
                              {sentence.substring(sentence.indexOf('系统检测到') + 5, sentence.indexOf('，'))}
                            </span>
                            <span style={{ color: 'black' }}>
                              {sentence.substring(sentence.indexOf('，'))}
                            </span>
                            {'.'}
                          </>
                        ) : (
                          <span>{sentence}.</span>
                        )}
                      </div>
                    ) : null
                  )}
                </div>
              </div>
            </Form.Item>
            <Form.Item label="品牌授权有效期" labelAlign="left" colon>
              <span>{formatTimestamp(data?.licenseStartTime)} ~ {formatTimestamp(data?.licenseEndTime)}</span>
            </Form.Item>
            <Form.Item label="授权范围" labelAlign="left" colon>
              <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start', marginTop: '4px' }}>
                <div style={{ marginTop: '-4px' }}>{data?.licenseType === 'all' ? '品牌下全部类目' : '指定类目'}</div>
                {data?.licenseType === 'part' && renderCategoryList()}
              </div>
            </Form.Item>
          </Form>
        </Loading>
        
        {/* 添加页脚区域 */}
        <div className={styles.drawerFooter}>
          {data?.operateList?.includes('audit') && (
            <Button type="primary" onClick={handleAudit}>审核</Button>
          )}
          <Button onClick={onClose} style={{ marginLeft: '8px' }}>关闭</Button>
        </div>
      </Drawer>

      <Dialog
        visible={previewVisible}
        onClose={() => setPreviewVisible(false)}
        footer={false}
        style={{ width: 'auto', maxWidth: '90%' }}
        v2
        closeMode={["close", "mask", "esc"]}
      >
        <div className={styles.previewContainer}>
          {currentImage && (
            <img 
              src={currentImage} 
              alt="预览图片" 
              className={styles.previewImage}
            />
          )}
          {data?.licenseUrlList && data.licenseUrlList.length > 1 && (
            <>
              <div 
                className={styles.navButton}
                style={{ left: '10px' }}
                onClick={handlePrev}
              >
                &lt;
              </div>
              <div 
                className={styles.navButton}
                style={{ right: '10px' }}
                onClick={handleNext}
              >
                &gt;
              </div>
            </>
          )}
        </div>
      </Dialog>

      {/* 添加审核对话框 */}
      <AuditDialog
        visible={auditDialogVisible}
        onClose={() => setAuditDialogVisible(false)}
        onOk={handleAuditSubmit}
      />
    </>
  );
};

const tableHeaderStyle = {
  padding: '8px',
  backgroundColor: '#f5f5f5',
  borderBottom: '1px solid #e8e8e8',
  textAlign: 'left' as const
};

const tableCellStyle = {
  padding: '8px',
  borderBottom: '1px solid #e8e8e8'
};

export default QualificationDrawer;
