import React, { useState, useEffect } from 'react';
import { Dialog, Form, Radio, Input, Field, Message } from '@alifd/next';
import { AuditStatus } from '@/types/merchantQualification';

interface AuditDialogProps {
  visible: boolean;
  onClose: () => void;
  onOk: (values: { auditStatus: AuditStatus; rejectReason?: string }) => void;
}

const AuditDialog: React.FC<AuditDialogProps> = ({
  visible,
  onClose,
  onOk,
}) => {
  const field = Field.useField();
  const [auditStatus, setAuditStatus] = useState<AuditStatus | null>(null);

  useEffect(() => {
    if (visible) {
      setAuditStatus(null);
      field.reset();
    }
  }, [visible, field]);

  const handleStatusChange = (value: AuditStatus) => {
    setAuditStatus(value);
    field.setValue('auditStatus', value);
  };

  const handleOk = async () => {
    try {
      if (!auditStatus) {
        Message.error('请选择审核结果');
        return;
      }
      
      if (auditStatus === AuditStatus.REJECTED) {
        const { errors } = await field.validatePromise(['rejectReason']);
        if (errors) {
          return;
        }
      }
      
      const values = field.getValues();
      onOk(values);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  return (
    <Dialog
      visible={visible}
      title="审核资质"
      onClose={onClose}
      onOk={handleOk}
      onCancel={onClose}
      style={{width: 400}}
    >
      <Form field={field} labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
        <Form.Item
          label="审核结果"
          required
        >
          <Radio.Group 
            name="auditStatus" 
            value={auditStatus}
            onChange={handleStatusChange}
          >
            <Radio value={AuditStatus.APPROVED}>通过</Radio>
            <Radio value={AuditStatus.REJECTED}>拒绝</Radio>
          </Radio.Group>
        </Form.Item>
        {auditStatus === AuditStatus.REJECTED && (
          <Form.Item
            label="拒绝原因"
            required
            requiredMessage="请输入拒绝原因"
          >
            <Input.TextArea 
              name="rejectReason"
              placeholder="请输入拒绝原因" 
              maxLength={20}
              showLimitHint
            />
          </Form.Item>
        )}
      </Form>
    </Dialog>
  );
};

export default AuditDialog;
