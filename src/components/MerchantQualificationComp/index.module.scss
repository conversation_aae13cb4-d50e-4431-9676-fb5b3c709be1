.cardQualificationList {
  background: #fff;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  .cardTitleContainer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    border-bottom: 1px solid #e8e8e8;
  }

  .cardTitle {
    color: #111;
    font-size: 18px;
    font-weight: 500;
    line-height: 26px;
  }
  
  .listContainer {
    padding: 16px 20px;
  }
}

.paginationWrapper {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 16px 20px;
  background: #fff;
  margin-top: 16px;
  border-radius: 4px;

  .totalText {
    margin-left: 16px;
    color: #666;
  }
}

.qualificationForm {
  :global {
    .next-col.next-col-14.next-form-item-control {
      display: flex;
      align-items: center;
    }
    
    .next-form-item {
      margin-bottom: 16px;
    }
  }
}

.previewContainer {
  position: relative;
  text-align: center;
  min-height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.previewImage {
  max-width: 100%;
  max-height: 80vh;
  object-fit: contain;
}

.navButton {
  position: absolute;
  top: 50%;
  cursor: pointer;
  font-size: 24px;
  background: rgba(0, 0, 0, 0.3);
  color: white;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateY(-50%);
  z-index: 10;
  transition: background 0.3s;
  
  &:hover {
    background: rgba(0, 0, 0, 0.5);
  }
}

.drawerFooter {
  display: flex;
  justify-content: center;
  padding: 16px 0;
  margin-top: 16px;
  border-top: 1px solid #e8e8e8;
}
