import React from 'react';
import { Pagination } from '@alifd/next';
import styles from '@/pages/MerchantQualificationManage/index.module.scss';

interface QualificationPaginationProps {
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };
  onChange: (current: number) => void;
  onPageSizeChange: (pageSize: number) => void;
}

const QualificationPagination: React.FC<QualificationPaginationProps> = ({
  pagination,
  onChange,
  onPageSizeChange,
}) => {
  return (
    <div>
      <Pagination
        total={pagination.total}
        current={pagination.current}
        pageSize={pagination.pageSize}
        className={styles.myPagination}
        type="normal"
        shape="normal"
        showJump
        pageSizeSelector="dropdown"
        pageSizeList={[10, 20]}
        totalRender={(total) => <span className={styles.totalText}> 共 {total} 条</span>}
        onChange={onChange}
        onPageSizeChange={onPageSizeChange}
      />
    </div>
  );
};

export default QualificationPagination;
