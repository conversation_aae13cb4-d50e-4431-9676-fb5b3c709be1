import React from 'react';
import { Dialog, Form, Input, Field } from '@alifd/next';

interface CancelDialogProps {
  visible: boolean;
  onClose: () => void;
  onOk: (reason: string) => void;
}

const CancelDialog: React.FC<CancelDialogProps> = ({
  visible,
  onClose,
  onOk,
}) => {
  const field = Field.useField();

  const handleOk = async () => {
    try {
      const { errors } = await field.validatePromise();
      if (errors) {
        return;
      }
      const values = field.getValues();
      onOk(values.reason);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  return (
    <Dialog
      visible={visible}
      title="取消授权"
      onClose={onClose}
      onOk={handleOk}
      onCancel={onClose}
      style={{width: 400}}
    >
      <Form field={field}>
        <Form.Item
          label="取消原因"
          required
          requiredMessage="请输入取消原因"
        >
          <Input.TextArea 
            name="reason"
            placeholder="请输入取消授权原因" 
            maxLength={20}
            showLimitHint
          />
        </Form.Item>
      </Form>
    </Dialog>
  );
};

export default CancelDialog;
