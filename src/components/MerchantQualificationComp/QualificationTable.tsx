import React from 'react';
import { Table, Button } from '@alifd/next';
import styles from './index.module.scss';

interface QualificationTableProps {
  loading: boolean;
  dataSource: any[];
  onView: (record: any) => void;
  onEdit: (record: any) => void;
}

const QualificationTable: React.FC<QualificationTableProps> = ({
  loading,
  dataSource,
  onView,
  onEdit,
}) => {
  const columns = [
    { title: '申请Id', dataIndex: 'applyId', width: 100 },
    { title: 'sellerId', dataIndex: 'sellerId', width: 100 },
    { title: '商家名称', dataIndex: 'merchantName', width: 150 },
    { title: '品牌名称', dataIndex: 'brandName', width: 150 },
    { title: '品牌logo', dataIndex: 'brandLogo', width: 100 },
    { title: '商标注册号', dataIndex: 'trademarkNo', width: 150 },
    { title: '授权状态', dataIndex: 'authStatus', width: 150 },
    { 
      title: '资质生效时间段', 
      dataIndex: 'effectiveTime', 
      width: 200,
      cell: (value, index, record) => (
        <span>{record.startTime} ~ {record.endTime}</span>
      )
    },
    { title: '失效范围', dataIndex: 'expireRange', width: 150 },
    { title: '审核状态', dataIndex: 'auditStatus', width: 100 },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 150,
      cell: (value, index, record) => (
        <div>
          <Button 
            text 
            type="primary" 
            onClick={() => onView(record)}
            style={{ marginRight: '8px' }}
          >
            查看
          </Button>
          <Button 
            text 
            type="primary" 
            onClick={() => onEdit(record)}
          >
            编辑
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className={styles.cardQualificationList}>
      <div className={styles.cardTitleContainer}>
        <span className={styles.cardTitle}>商家资质列表</span>
      </div>
      <div className={styles.listContainer}>
        <Table
          loading={loading}
          dataSource={dataSource}
          columns={columns}
          primaryKey="applyId"
          emptyContent={<div>暂无数据</div>}
        />
      </div>
    </div>
  );
};

export default QualificationTable;