import React, { useState, useEffect } from 'react';
import { Table } from '@alifd/next';
import type { TableProps } from '@alifd/next/types/table';

interface AdaptiveTableProps extends TableProps {
  adaptiveThreshold?: number; // 自适应阈值，默认1410
}

const AdaptiveTable: React.FC<AdaptiveTableProps> = ({
  adaptiveThreshold = 1410,
  ...tableProps
}) => {
  const [tableWidth, setTableWidth] = useState(window.innerWidth - 48);

  useEffect(() => {
    const handleResize = () => {
      setTableWidth(window.innerWidth - 48);
    };

    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  return (
    <Table
      {...tableProps}
      style={{ overflow: 'unset' }}
      tableLayout={tableWidth > adaptiveThreshold ? 'auto' : 'fixed'}
    />
  );
};

export default AdaptiveTable; 