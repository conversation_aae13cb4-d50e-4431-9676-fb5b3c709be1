import React, { useState, useEffect } from 'react';
import { Select, TreeSelect } from '@alifd/next';
import { queryCategoryLeaf, queryCategoryLeafNotFull, queryCategoryTreeLeaf } from '@/services';
import { transformTreeData } from '@/pages/Utils';
// import { IQueryCategoryLeafParams, queryCategoryLeafResult } from '@/types';

interface CategoryOption {
  value: string;
  label: string;
  children?: CategoryOption[];
}

interface CategoryTreeSelectProps {
  value?: string[];
  onChange?: (value: string[], option: any) => void;
  placeholder?: string;
  style?: React.CSSProperties;
  mode?: 'single' | 'multiple';
  hasClear?: boolean;
  isTree?: boolean;
  poolId?: number;
  isFull?: boolean;
}

const CategoryTreeSelect: React.FC<CategoryTreeSelectProps> = ({
  value,
  onChange,
  placeholder = '请搜索并选择类目',
  style = { width: '100%' },
  mode = 'single',
  hasClear = true,
  isTree = false,
  poolId,
  isFull = true
}) => {
  const [categories, setCategories] = useState<CategoryOption[]>([]);
  const fetchCategories = async (value?: string) => {
    try {
      const params = {
        poolId: poolId,
      }
      
      let response;
      if (isTree) {
        response = await queryCategoryTreeLeaf(params);
      } else {
        if (isFull) {
          response = await queryCategoryLeaf({ categoryName: value || '' });
        } else {
          response = await queryCategoryLeafNotFull({ 
            categoryName: value || '',
            poolId: poolId 
          });
        }
      }
      
      if (response?.data?.length) {
        // Transform data based on isTree flag
        const transformedData = isTree
          ? transformTreeData(response.data)
          : response?.data?.map((item: any) => ({
            value: item,
            label: item,
          })) || [];
        setCategories(transformedData);
      }
    } catch (error) {
      console.error('获取类目数据失败:', error);
    }
  };

  useEffect(() => {
    // 树形结构 需要默认获取所有叶子类目
    isTree && fetchCategories();
  }, [isTree, poolId]);

  const handleSearch = (value: string) => {
    fetchCategories(value);
  };

  const itemRender2 = (item, searchKey) => {
    let label = item.label;
    if (searchKey && searchKey.length) {
      const key = searchKey.replace(/[-/\^$*+?.()|[]{}]/g, '$&');
      const reg = new RegExp(`(${key})`, 'ig');
      label = label.replace(reg, (x) => `<span class="next-select-highlight">${x}</span>`);
    }
    return <span dangerouslySetInnerHTML={{ __html: label }} />;
  };

  if (isTree) {
    return (
      <TreeSelect
        treeCheckable
        dataSource={categories as any}
        multiple={mode === 'multiple'}
        treeCheckedStrategy="child"
        showSearch
        onChange={onChange}
        placeholder={placeholder}
        notFoundContent="暂无数据"
        style={style}
        treeDefaultExpandAll
        followTrigger={true}
        hasClear={hasClear}
        value={value}
      />
    );
  }

  return (
    <Select
      showSearch
      hasClear={hasClear}
      followTrigger={true}
      value={value}
      dataSource={categories as any}
      mode={mode}
      filterLocal={false}
      onSearch={handleSearch}
      onChange={onChange}
      itemRender={itemRender2}
      placeholder={placeholder}
      notFoundContent="暂无数据"
      style={style}
      maxTagCount={1}
    />
  );
};

export default CategoryTreeSelect;
