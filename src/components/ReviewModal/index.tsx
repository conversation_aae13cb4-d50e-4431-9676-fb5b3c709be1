import React, { useState, useEffect } from 'react';
import { Dialog, Button, Input, Message } from '@alifd/next';

interface ReviewModalProps {
  modalTitle?: string;
  visible: boolean;
  onClose: () => void;
  onApprove: (comment?: string) => Promise<void>;
  onReject: (comment?: string) => Promise<void>;
}

const ReviewModal: React.FC<ReviewModalProps> = ({ modalTitle, visible, onClose, onApprove, onReject }) => {
  const [rejectReason, setRejectReason] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isReject, setIsReject] = useState(false);

  useEffect(() => {
    if (visible) {
      setIsReject(false);
      setRejectReason('');
    }
  }, [visible]);

  const handleSubmit = async () => {
    if (isReject && !rejectReason.trim()) {
      Message.error('请填写拒绝原因');
      return;
    }

    setIsLoading(true);
    try {
      if (isReject) {
        await onReject(rejectReason);
      } else {
        await onApprove();
      }
      onClose();
    } catch (error) {
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog
      visible={visible}
      title={modalTitle || '审核'}
      onClose={onClose}
      style={{ width: 500 }}
      footer={(
        <div style={{ display: 'flex', justifyContent: 'flex-end', gap: '10px' }}>
          <Button type="primary" onClick={handleSubmit} loading={isLoading}>
            确定
          </Button>
        </div>
      )}
    >
      <div style={{ marginBottom: '20px' }}>
        <div style={{ margin: '10px 0' }}>
          <label>
            <input
              type="radio"
              checked={!isReject}
              onChange={() => setIsReject(false)}
            />
            通过
          </label>
          <label style={{ marginLeft: '20px' }}>
            <input
              type="radio"
              checked={isReject}
              onChange={() => setIsReject(true)}
            />
            不通过
          </label>
        </div>
      </div>
      {isReject && (
        <div style={{ marginBottom: '20px' }}>
          <Input.TextArea
            placeholder="请输入不通过原因"
            value={rejectReason}
            onChange={setRejectReason}
            style={{ width: '450px' }}
          />
        </div>
      )}
    </Dialog>
  );
};

export default ReviewModal; 