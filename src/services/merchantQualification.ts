import request from '@ali/bzb-request';
import { 
  IQueryQualificationListParams, 
  QueryQualificationListResult,
  IAuditQualificationParams,
  QualificationItem 
} from '@/types/merchantQualification';
import { IResultOthers, commonApi } from '@/services/index';

// 查询商家资质列表
export const queryMerchantQualificationList = (params: IQueryQualificationListParams) =>
  commonApi<IQueryQualificationListParams, QueryQualificationListResult>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.qualification.list',
    params,
  );

// 查询商家资质详情
export const queryQualificationDetail = (id: number) =>
  commonApi<{ id: number }, { data: QualificationItem }>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.brand.licence.query',
    { id },
  );

// 审核商家资质
export const auditQualification = (params: IAuditQualificationParams) =>
  commonApi<IAuditQualificationParams, IResultOthers>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.qualification.audit',
    params,
  );

// 取消商家资质授权
export const cancelQualification = (params: { id: number; reason: string }) =>
  commonApi<{ id: number; reason: string }, IResultOthers>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.brand.licence.reject',
    params,
  );

// 汇总审核状态个数
export const queryQualificationCount = (params: {
  sellerName?: string;
  brandName?: string;
  licenseStatus?: string;
  auditStatus?: string;
}) =>
  commonApi<{
    sellerName?: string;
    brandName?: string;
    licenseStatus?: string;
    auditStatus?: string;
  }, { data: { countList: Array<{ type: string; count: number }> } }>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.brand.licence.count',
    params,
  );
