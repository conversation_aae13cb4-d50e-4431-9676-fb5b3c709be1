import request from '@ali/bzb-request';
import {
  IQueryItemsPoolListParams,
  queryItemsPoolListResult,
  IQueryItemsPoolListCountParams,
  queryItemsPoolListCountResult,
  IQueryItemInfoParams,
  queryItemInfoResult,
  IQueryItemsPoolBaseInfoParams,
  queryItemsPoolBaseInfoResult,
  IEditItemsPoolRuleParams,
  IQueryItemsListParams,
  queryItemslListResult,
  IAuditItemParams,
  IItemOperateParams,
  IQueryItemOperateLogParams,
  queryItemOperateLogResult,
  IQueryProjectListParams,
  queryProjectListResult,
  IQueryCategoryLeafParams,
  queryCategoryLeafResult,
  IQueryBrandParams,
  queryBrandResult,
  IQueryEnterpriseListParams,
  queryEnterpriseListResult,
  IQueryEnterpriseDetailParams,
  queryEnterpriseDetailResult,
  IQueryProjectDetailParams,
  queryProjectDetailResult,
  IQueryMerchantListParams,
  queryMerchantListResult,
  IQueryMerchantDetailParams,
  queryMerchantDetailResult,
  IEditSkuParams,
  IQueryServicerListParams,
  queryServicerListResult,
  IQueryServicerDetailParams,
  queryServicerDetailResult,
  queryItemsPoolSkuListResult,
  queryEnterpriseSelectResult,
  queryEnterpriseSelectParams,
  queryEnterpriseOrderListParams,
  queryEnterpriseOrderListResult,
  queryEnterpriseOrderDetailParams,
  queryEnterpriseOrderDetailResult,
  queryContractBaseInfoResult,
  queryEnterpriseSelectListResult,
  queryEnterpriseSelectListExtendResult,
  queryTaobaoMerchantInfoResult,
  queryCorporateBillListParams,
  queryCorporateBillListResult,
  queryCorporateBillDetailParams,
  queryCorporateBillDetailResult,
  queryUpdateBillStatusParams,
  queryUpdateBillStatusResult,
  queryCorporateBillItemParams,
  queryCorporateBillItemResult,
  queryCreateBatchOrderResult,
  queryCreateBatchOrder,
  queryApplySalesInvoice,
  queryApplySalesInvoiceResult,
  queryGetSalesInvoiceResult,
  queryGetSalesInvoice,
} from '@/types';

export interface IResultOthers {
  data: any;
  success: any;
  message: string;
}

export function commonApi<D, T>(api: string, data?: D, otherOptions?: any): Promise<T & IResultOthers> {
  return new Promise<T & IResultOthers>((resolve) => {
    const env = /\/\/pre-/.test(window.location.href) ? 'pre' : 'prod'; // 判断环境
    request(api,{
      method: 'POST',
      env,
      data,
      ...otherOptions,
    })
      .then((res: any) => {
        let theData = res?.data;
        if (typeof theData !== 'object') {
          theData = { data: theData };
        }
        resolve({...theData});
      })
      .catch((error: any) => {
        const resultRetCode = error?.data?.code || error?.ret?.[0]?.split('::')?.[0];
        const resultMessage: string = error?.data?.message || error?.ret?.[0]?.split('::')?.[1];

        const returnData: any & IResultOthers = {
          resultSuccess: false,
          resultRetCode,
          resultMessage,
        };
        resolve(returnData);
      });
  });
}

// 项目列表查询
export const queryProjectList = (params: IQueryProjectListParams) =>
  commonApi<IQueryProjectListParams, queryProjectListResult>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.project.list',
    params,
  );

// 商品总池数量查询
export const queryItemsPoolListCount = (params: IQueryItemsPoolListCountParams) =>
  commonApi<IQueryItemsPoolListCountParams, queryItemsPoolListCountResult>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.item.list.count',
    params,
  );

// 商品总池列表查询
export const queryItemsPoolList = (params: IQueryItemsPoolListParams) =>
  commonApi<IQueryItemsPoolListParams, queryItemsPoolListResult>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.item.list',
    params,
  );

  // 商品池sku 列表查询
  export const queryItemsPoolSkuList = (params: {itemId: number}) =>
  commonApi<{itemId: number}, queryItemsPoolSkuListResult>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.sku.list',
    params,
  );

// 商品查询
export const queryItemInfo = (params: IQueryItemInfoParams) =>
  commonApi<IQueryItemInfoParams, queryItemInfoResult>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.item.query',
    params,
  );

// 编辑sku
export const editSku = (params: IEditSkuParams) =>
  commonApi<IEditSkuParams, IResultOthers>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.pool.sku.edit',
    params,
  );

// 项目下拉列表查询
export const queryProjectSelectList = (params: {}) =>
  commonApi<{},  any>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.project.pool.query',
    params,
  );

// 商品池基础信息查询
export const queryItemsPoolBaseInfo = (params: IQueryItemsPoolBaseInfoParams) =>
  commonApi<IQueryItemsPoolBaseInfoParams, queryItemsPoolBaseInfoResult>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.pool.query',
    params,
  );

// 商品入池规则编辑
export const editItemsPoolRule = (params: IEditItemsPoolRuleParams) =>
  commonApi<IEditItemsPoolRuleParams, IResultOthers>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.pool.rule.edit',
    params,
  );

// 商品筛选查询
export const queryItemsList = (params: IQueryItemsListParams) =>
  commonApi<IQueryItemsListParams, queryItemslListResult>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.enterprise.item.list',
    params,
  );

// 审核
export const auditItem = (params: IAuditItemParams) =>
  commonApi<IAuditItemParams, IResultOthers & { data : {successCount: number, failCount: number, failList: any[] }}>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.audit',
    params,
  );

// 加入商品池
export const itemOperate = (params: IItemOperateParams) =>
  commonApi<IItemOperateParams, IResultOthers>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.item.pool.add',
    params,
  );

// 下架
export const itemOffline = (params: IItemOperateParams) =>
    commonApi<IItemOperateParams, IResultOthers>(
      'bzb.api.tb_enterprise_purchase.cobweb.ego.item.pool.offline',
      params,
    );

    // 上架
export const itemOnline = (params: IItemOperateParams) =>
  commonApi<IItemOperateParams, IResultOthers>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.item.pool.online',
    params,
  );

// 商品池操作记录查询
export const queryItemOperateLog = (params: IQueryItemOperateLogParams) =>
  commonApi<IQueryItemOperateLogParams, queryItemOperateLogResult>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.pool.operate.list',
    params,
   );

// 全量叶子类目查询
export const queryCategoryLeaf = (params: IQueryCategoryLeafParams) =>
  commonApi<IQueryCategoryLeafParams, queryCategoryLeafResult>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.category.query',
    params,
  )

// 非全量叶子类目查询
export const queryCategoryLeafNotFull = (params: IQueryCategoryLeafParams & {poolId?: number}) =>
  commonApi<IQueryCategoryLeafParams, queryCategoryLeafResult>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.enterprise.add.category.list',
    params,
  )


  // 叶子类目树查询
export const queryCategoryTreeLeaf = (params: {poolId?: number}) =>
  commonApi<{poolId?: number}, queryCategoryLeafResult>(
    'bzb.api.tb_enterprise_purchase.cobweb.enterprise.wait.category.query',
    params,
  );


  // 品牌查询
export const queryBrand = (params: IQueryBrandParams) =>
  commonApi<IQueryBrandParams, queryBrandResult>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.brand.query',
    params,
  );

// 关键词品牌查询限定范围
export const queryBrandLimit = (params: IQueryBrandParams & {poolId?: number}) =>
  commonApi<IQueryBrandParams & {poolId?: number}, queryBrandResult>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.enterprise.add.brand.list',
    params,
  );

  // 品牌下拉全量查询
  export const queryBrandAll = (params: {poolId?: number}) =>
  commonApi<{poolId?: number}, queryBrandResult>(
    'bzb.api.tb_enterprise_purchase.cobweb.enterprise.wait.brand.query',
    params,
  );

  // 企业管理
  // 企业列表查询
  export const queryEnterpriseList = (params: IQueryEnterpriseListParams) =>
  commonApi<IQueryEnterpriseListParams, queryEnterpriseListResult>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.enterprise.list',
    params,
  );

 // 企业详情查询
 export const queryEnterpriseDetail = (params: IQueryEnterpriseDetailParams) =>
  commonApi<IQueryEnterpriseDetailParams, queryEnterpriseDetailResult>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.enterprise.query',
    params,
  );

// 企业编辑保存
export const addEnterprise = (params: queryEnterpriseDetailResult) =>
  commonApi<queryEnterpriseDetailResult, IResultOthers>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.enterprise.save',
    params,
  );

// 企业删除
export const deleteEnterprise = (params: {roleId: number, editVersion: number}) =>
  commonApi<{roleId: number, editVersion: number}, IResultOthers>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.enterprise.delete',
    params,
 );

 // 企业失效
 export const invalidEnterprise = (params: {roleId: number, editVersion: number}) =>
  commonApi<{roleId: number, editVersion: number}, IResultOthers>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.enterprise.invalidate',
    params,
 );

 // 项目提交审核
 export const submitProjectAudit = (params: {projectId: number, editVersion: number}) =>
  commonApi<{projectId: number, editVersion: number}, IResultOthers>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.project.submit',
    params,
 );

 // 项目撤回审核
 export const cancelProjectAudit = (params: {projectId: number, editVersion: number}) =>
  commonApi<{projectId: number, editVersion: number}, IResultOthers>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.project.cancel',
    params,
 );

 // 执行项目审核通过
 export const executeProjectAuditApprove = (params: {projectId: number, editVersion: number}) =>
  commonApi<{projectId: number, editVersion: number}, IResultOthers>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.project.approve',
    params,
 );

// 执行项目审核不通过
export const executeProjectAuditReject = (params: {projectId: number, editVersion: number, remark: string}) =>
  commonApi<{projectId: number, editVersion: number, remark: string}, IResultOthers>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.project.reject',
    params,
 );

 // 项目删除
 export const deleteProject = (params: {projectId: number, editVersion: number}) =>
  commonApi<{projectId: number, editVersion: number}, IResultOthers>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.project.delete',
    params,
 );

 // 项目关联商品池查询
 export const queryProjectItemsPool = (params: {projectId: number}) =>
  commonApi<{projectId: number}, queryProjectListResult>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.project.pool.query',
    params,
  );

  // 新增项目商品池
  export const addProjectItemsPool = (params: {projectId: number }) =>
  commonApi<{projectId: number}, IResultOthers>(
    'bzb.api.tb_enterprise_purchase.cobweb.enterprise.pool.add',
    params,
  );

 // 项目失效
  export const invalidProject = (params: {projectId: number, editVersion: number}) =>
  commonApi<{projectId: number, editVersion: number}, IResultOthers>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.project.invalidate',
    params,
 );

 // 执行企业审核通过
 export const executeEnterpriseAuditPass = (params: {roleId: number, editVersion: number}) =>
  commonApi<{roleId: number, editVersion: number}, IResultOthers>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.enterprise.approve',
    params,
 );

 // 执行企业审核不通过
 export const executeEnterpriseAuditFail = (params: {roleId: number, editVersion: number, remark: string}) =>
  commonApi<{roleId: number, editVersion: number, remark: string}, IResultOthers>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.enterprise.reject',
    params,
 );

 // 撤回审核
 export const cancelEnterpriseAudit = (params: {roleId: number, editVersion: number}) =>
  commonApi<{roleId: number, editVersion: number}, IResultOthers>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.enterprise.cancel',
    params,
 );

// 项目查询
export const queryProjectDetail  = (params: IQueryProjectDetailParams) =>
  commonApi<IQueryProjectDetailParams, queryProjectDetailResult>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.project.query',
    params,
  );

// 新增添加项目
export const addProject = (params: queryProjectDetailResult) =>
  commonApi<queryProjectDetailResult, IResultOthers>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.project.add',
    params,
  );

// 编辑项目
export const editProject = (params: queryProjectDetailResult) =>
  commonApi<queryProjectDetailResult, IResultOthers>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.project.edit',
    params,
  );
// 查询合同基础信息
export const queryContractBaseInfo = (params: {contractId: string}) =>
  commonApi<{contractId: string}, queryContractBaseInfoResult>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.contract.query',
    params,
  );

  // 企业下拉列表查询
  export const queryEnterpriseSelectList = (params: {roleId?: number | null}) =>
  commonApi<{roleId?: number | null}, queryEnterpriseSelectListResult>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.enterprise.select',
    params,
  );

  // 企业下拉扩展查询
  export const queryEnterpriseSelectListExtend = (params: {name?: string}) =>
  commonApi<{name?: string}, queryEnterpriseSelectListExtendResult>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.enterprise.select.extend',
    params,
  );

  // 企业提交审核
  export const submitEnterpriseAudit = (params: {roleId: number, editVersion: number}) =>
  commonApi<{roleId: number, editVersion: number}, IResultOthers>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.enterprise.submit',
    params,
  );

// 商家相关接口
// 商家列表查询
export const queryMerchantList = (params: IQueryMerchantListParams) =>
  commonApi<IQueryMerchantListParams, queryMerchantListResult>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.seller.list',
    params,
  );

// 商家详细信息查询
export const queryMerchantDetail = (params: IQueryMerchantDetailParams) =>
  commonApi<IQueryMerchantDetailParams, queryMerchantDetailResult>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.seller.query',
    params,
  );

  // 查询淘系商家信息
  export const queryTaobaoMerchantInfo = (params: {sellerId: number}) =>
  commonApi<{sellerId: number}, queryTaobaoMerchantInfoResult>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.seller.queryTaoSeller',
    params,
  );

  // 新增/编辑商家
  export const addMerchant = (params: queryMerchantDetailResult) =>
  commonApi<queryMerchantDetailResult, IResultOthers>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.seller.save',
    params,
  );

  // 商家档位下拉接口
  export const queryRateSellerLevelSelect = (params: {}) =>
    commonApi<queryMerchantDetailResult, IResultOthers>(
      'bzb.api.tb_enterprise_purchase.cobweb.ego.seller.queryRateSellerLevelSelect',
      {},
    );

  // 项目档位下拉接口
  export const queryRateProjectLevelSelect = (params: {}) =>
    commonApi<{}, IResultOthers>(
      'bzb.api.tb_enterprise_purchase.cobweb.ego.project.queryRateProjectLevelSelect',
      {},
    );


  // 商家删除
  export const deleteMerchant = (params: {roleId: number, editVersion: number}) =>
  commonApi<{roleId: number, editVersion: number}, IResultOthers>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.seller.delete',
    params,
  );

  // 商家失效
  export const invalidMerchant = (params: {roleId: number, editVersion: number}) =>
  commonApi<{roleId: number, editVersion: number}, IResultOthers>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.seller.invalidate',
    params,
  );

  // 商家提交审核
  export const submitMerchantAudit = (params: {roleId: number, editVersion: number}) =>
  commonApi<{roleId: number, editVersion: number}, IResultOthers>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.seller.submit',
    params,
  );

  // 商家撤回审核
  export const cancelMerchantAudit = (params: {roleId: number, editVersion: number}) =>
  commonApi<{roleId: number, editVersion: number}, IResultOthers>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.seller.cancel',
    params,
  );

  // 商家审核驳回
  export const rejectMerchantAudit = (params: {roleId: number, editVersion: number, remark: string}) =>
  commonApi<{roleId: number, editVersion: number, remark: string}, IResultOthers>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.seller.reject',
    params,
  );

  // 企业 商家 项目审核 通过 拒绝 撤销 操作三合一接口
  export const auditAll = (params: {
    instanceType: 'bpms',
    targetType: 'project' | 'enterprise' | 'seller' | 'servicer' | 'brand_licence',
    auditType: 'approve' | 'reject' | 'cancel',
    mark?: string,
    targetIdList: string[],
  }) =>
  commonApi<{
    instanceType: 'bpms',
    targetType: 'project' | 'enterprise' | 'seller' | 'servicer',
    auditType: 'approve' | 'reject' | 'cancel',
    mark?: string,
    targetIdList: string[],
  }, IResultOthers>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.audit',
    params,
  );

  // 商家审核通过
  export const approveMerchantAudit = (params: {roleId: number, editVersion: number}) =>
  commonApi<{roleId: number, editVersion: number}, IResultOthers>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.seller.approve',
    params,
  );

// 服务商相关接口
// 服务商列表查询
export const queryServicerList = (params: IQueryServicerListParams) =>
  commonApi<IQueryServicerListParams, queryServicerListResult>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.servicer.list',
    params,
  );

// 服务商查询
export const queryServicerDetail = (params: IQueryServicerDetailParams) =>
  commonApi<IQueryServicerDetailParams, queryServicerDetailResult>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.servicer.query',
    params,
  );

  // 新增/编辑服务商
 export const addServicer = (params: queryServicerDetailResult) =>
  commonApi<queryServicerDetailResult, IResultOthers>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.servicer.add',
    params,
  );

// 企业下拉列表查询
export const queryEnterpriseSelect = (params?: queryEnterpriseSelectParams) =>
  commonApi<queryEnterpriseSelectParams, queryEnterpriseSelectResult>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.enterprise.select',
    params,
  );

// 企业订单列表
export const queryEnterpriseOrderList = (params?: queryEnterpriseOrderListParams) =>
  commonApi<queryEnterpriseOrderListParams, queryEnterpriseOrderListResult>(
    'bzb.api.tb_enterprise_purchase.ego.order.list',
    params,
  );

// 企业订单详情
export const queryEnterpriseOrderDetail = (params?: queryEnterpriseOrderDetailParams) =>
  commonApi<queryEnterpriseOrderDetailParams, queryEnterpriseOrderDetailResult>(
    'bzb.api.tb_enterprise_purchase.ego.order.detail',
    params,
  );
// 登录用户信息
export const getUserInfo = () =>
  commonApi<{}, IResultOthers>(
    'bzb.api.ai.conversation.create',
    {},
  )

// 企业账单列表与详情
export const getCorporateBillList = (params?: queryCorporateBillListParams) =>
  commonApi<queryCorporateBillListParams, queryCorporateBillListResult>(
    'bzb.api.tb_enterprise_purchase.ego.batch.settle.queryByParamsPage',
    params,
  )
export const getCorporateBillItem = (params?: queryCorporateBillItemParams) =>
  commonApi<queryCorporateBillItemParams, queryCorporateBillItemResult>(
    'bzb.api.tb_enterprise_purchase.ego.batch.settle.queryOneByNo',
    params,
  )
export const getCorporateBillDetail = (params?: queryCorporateBillDetailParams) =>
  commonApi<queryCorporateBillDetailParams, queryCorporateBillDetailResult>(
    'bzb.api.tb_enterprise_purchase.ego.batch.settle.queryBillsByBatchNoPage',
    params,
  )

// 代客确认
export const updateBillStatus = (params?: queryUpdateBillStatusParams) =>
  commonApi<queryUpdateBillStatusParams, queryUpdateBillStatusResult>(
    'bzb.api.tb_enterprise_purchase.ego.batch.settle.updateStatus',
    params,
  )

// 创建账单
export const createBatchOrder = (params?: queryCreateBatchOrder) =>
  commonApi<queryCreateBatchOrder, queryCreateBatchOrderResult>(
    'bzb.api.tb_enterprise_purchase.ego.batch.settle.createBatchOrder',
    params,
  )
export const applySalesInvoice = (params?: queryApplySalesInvoice) =>
  commonApi<queryApplySalesInvoice, queryApplySalesInvoiceResult>(
    'bzb.api.tb_enterprise_purchase.ego.invoice.applySalesInvoice',
    params,
  )
export const getSalesInvoice = (params?: queryGetSalesInvoice) =>
  commonApi<queryGetSalesInvoice, queryGetSalesInvoiceResult>(
    'bzb.api.tb_enterprise_purchase.ego.invoice.getSalesInvoice',
    params,
  )

// 添加商家资质相关接口
export const queryMerchantQualificationList = (params?: queryEnterpriseOrderDetailParams) =>
  commonApi<queryEnterpriseOrderDetailParams, queryEnterpriseOrderDetailResult>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.brand.licence.list',
    params,
  )

// 品牌授权申请详情查询
export const queryMerchantQualificationDetail = ({ id: number }) =>
  commonApi<{ id: number }, queryEnterpriseOrderDetailResult>(
    'bzb.api.tb_enterprise_purchase.cobweb.ego.brand.licence.query',
    { id : number },
  )