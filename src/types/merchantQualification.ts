// 商家资质状态
export enum QualificationStatus {
  VALID = 'VALID',
  INVALID = 'INVALID'
}

// 审核状态
export enum AuditStatus {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED'
}

// 商家资质信息
export interface QualificationItem {
  applyId: string;
  sellerId: string;
  merchantName: string;
  brandName: string;
  brandLogo: string;
  trademarkNo: string;
  authStatus: QualificationStatus;
  startTime: string;
  endTime: string;
  expireRange: string;
  auditStatus: AuditStatus;
  editVersion: number;
  attachments?: Array<{
    fileId: string;
    fileName: string;
    fileUrl: string;
  }>;
  rejectReason?: string;
}

// 查询参数
export interface IQueryQualificationListParams {
  currentPage: number;
  pageSize: number;
  status?: QualificationStatus;
  sellerId?: string;
  auditStatus?: AuditStatus;
  brandName?: string;
}

// 查询结果
export interface QueryQualificationListResult {
  success: boolean;
  data: {
    dataList: QualificationItem[];
    totalCount: number;
  };
  message?: string;
}

// 审核参数
export interface IAuditQualificationParams {
  applyId: string;
  editVersion: number;
  auditStatus: AuditStatus;
  rejectReason?: string;
}