export enum OperationMode {
  SINGLE_PRODUCT = 'SINGLE_PRODUCT',
  PRODUCT_POOL = 'PRODUCT_POOL',
}
export interface IQueryItemsPoolListParams {
  title?: string; // 商品标题
  itemId?: string; // 商品ID
  applyStatus?: string; // 报名状态
  auditStatus?: string; // 审核状态
  leafCategoryName?: string; // 叶子类目名称
  brandName?: string; // 品牌名称
  createStartTime?: string; // 创建开始时间
  createEndTime?: string; // 创建结束时间
  sellerName?: string; // 商家名称
  pageSize: number; // 每页数量
  currentPage: number; // 当前页
}

export interface ProductItem {
  title: string; // 商品标题
  itemId: number; // 商品ID
  itemDetailUrl: string; // 商品详情页URL
  imageUrl: string; // 商品图片URL
  brandName: string; // 品牌名称
  leafCategoryName: string; // 叶子类目名称
  taxCode: string; // 税收编码
  taxRate: string; // 税率
  egoTaxCode: string; // 自我税收编码
  egoTaxRate: string; // 自我税率
  priceRange: string; // 价格范围
  referencePriceRange: string; // 参考价格范围
  egoPriceRange: string; // 自我价格范围
  quantity: number; // 库存数量
  gmtCreate: string; // 创建时间
  egoItemStatus: 'normal' | string; // 商品状态
  operateList: string[]; // 可进行的操作列表
}

export interface queryItemsPoolListResult {
  dataList: ProductItem[];
}

export interface IQueryItemsPoolListCountParams {
  title?: string; // 商品标题
  itemId?: number; // 商品ID
  applyStatus?: string; // 报名状态
  auditStatus?: string; // 审核状态
  leafCategoryName?: string; // 叶子类目名称
  brandName?: string; // 品牌名称
  createStartTime?: string; // 创建开始时间
  createEndTime?: string; // 创建结束时间
  sellerName?: string; // 商家名称
  pageSize: number; // 每页数量
  currentPage: number; // 当前页
}

export type CountItem = {
  type: string; // 类型
  count: number; // 计数
};

export interface queryItemsPoolListCountResult {
  countList: CountItem[];
}

export interface IQueryItemInfoParams {
  poolId: number;
  itemId: number; // 商品 ID
}

interface SkuItem {
  enable?: unknown;
  skuId: string; // SKU ID
  skuImageUrl: string; // SKU 图片 URL
  prop: string; // SKU 属性
  skuPrice: string; // SKU 价格
  referencePrice: string; // 参考价格
  egoPrice: string;
  ratioPrice: string;
}

export interface Product {
  imageUrl: string | undefined;
  egoReferencePrice: any;
  egoPrice: any;
  title: string; // 商品标题
  itemId: number; // 商品 ID
  itemDetailUrl: string; // 商品详情页 URL
  skuList: SkuItem[]; // SKU 列表
  egoItemStatus: string; // 商品状态
  leafCategoryName?: string; // 后台类目（叶子类目名称）
  outLeafCategoryName?: string; // 前台类目
}

export interface queryItemInfoResult {
  data: Product;
}

export interface IQueryItemsPoolBaseInfoParams {
  // projectId: number;
  poolId: number;
}

interface PoolRule {
  discountRateReference: number; // 折扣率参考
  discountRateOrigin: number; // 原始折扣率
  shipTime: number; // 发货时间
  taxRateDiffType: string; // 税率差异类型
}

interface Project {
  projectId: number; // 项目 ID
  projectName: string; // 项目名称
  enterpriseName: string; // 企业名称
}

export interface PoolData {
  poolId: number; // 池 ID
  poolRule: PoolRule; // 池规则
  project: Project; // 项目
  countList: CountItem[]; // 计数列表
}

export interface queryItemsPoolBaseInfoResult {
  data: PoolData;
}

export interface IEditItemsPoolRuleParams {
  poolId: number; // 池 ID
  discountRateReference: number; // 折扣率参考
  discountRateOrigin: number; // 原始折扣率
  shipTime: number; // 发货时间
}

export interface IQueryItemsListParams {
  poolId: number; // 池 ID
  queryType: string; // 查询类型
  title?: string; // 商品标题
  itemId?: number; // 商品ID
  egoItemStatus?: string; // 自我商品状态
  leafCategoryNameList?: string[]; // 叶子类目名称列表
  brandNameList?: string[]; // 品牌名称列表
  minEgoPrice?: string; // 最低自我价格
  maxEgoPrice?: string; // 最高自我价格
  minQuantity?: number; // 最低数量
  itemIdList?: number[]; // 商品ID集合
  sellerIdList?: number[]; // 卖家ID集合
  sellerName?: string; // 商家名称
  currentPage: number; // 当前页
  pageSize: number; // 每页数量
}

export interface queryItemslListResult {
  data: {
    dataList: Product[];
    totalCount: number;
  };
}

export interface IAuditItemParams {
  targetType: 'seller' | 'item' | 'enterprise' | 'pool' | 'servicer'; // 目标类型，例如 "seller"
  targetIdList: string[]; // 目标 ID 列表，例如 ["123456"]
  auditType: 'approve' | 'reject'; // 操作类型，例如 "approve"
  mark?: string; // 备注
}

export interface IItemOperateParams {
  poolId: number; // 池 ID
  itemIdList: number[]; // 商品 ID 列表
  itemIdSkuMap?: {
    // 商品 ID 与 SKU 映射
    [key: number]: string[]; // 商品 ID 对应的 SKU 列表
  };
  operate?: 'add' | 'down' | 'up'; // 操作类型 add 加入商品池，down 下架，up 上架
}

export interface IQueryItemOperateLogParams {
  poolId: number; // 池 ID
  currentPage: number; // 当前页
  pageSize: number; // 每页数量
}

export interface queryItemOperateLogResult {
  dataList: {
    operateTime: string; // 操作时间
    operator: string; // 操作人
    operation: string; // 操作类型
    details: string; // 操作详情
  }[];
}

export interface IQueryProjectListParams {
  enterpriseId: number;
  currentPage: number;
  pageSize: number;
}

interface Enterprise {
  enterpriseId: number; // 企业 ID
  shortName: string; // 企业简称
}

interface ItemPool {
  itemTotalNum: number; // 商品总数
}

interface Contract {
  contractId: number; // 合同 ID
  contractAmount: number; // 合同金额
}

export interface ProjectDetail {
  projectId: number; // 项目 ID
  poolId: number; // 池 ID
  projectName: string; // 项目名称
  enterprise: Enterprise; // 企业信息
  itemPool: ItemPool; // 商品池信息
  contract: Contract; // 合同信息
  gmtCreate: string; // 创建时间
  gmtModified: string; // 修改时间
  updateOperator: string; // 更新操作人
  operateList: string[]; // 可进行的操作列表
  effectiveStatus: string; // 有效状态
  status: string; // 状态
}

export interface queryProjectListResult {
  data: {
    dataList: ProjectDetail[];
    totalCount: number;
  };
}

export interface IQueryCategoryLeafParams {
  categoryName: string;
}

export interface queryCategoryLeafResult {
  data: string[];
}

export interface IQueryBrandParams {
  brandName: string;
}

export interface queryBrandResult {
  brandNameList: string[];
}

export interface IQueryEnterpriseListParams {
  currentPage: number;
  pageSize: number;
}

export interface EnterpriseItem {
  enterpriseId: number;
  shortName: string;
  projectList: Project[];
  gmtCreate: string;
  gmtModified: string;
  updateOperator: string;
  operateList: string[];
  effectiveStatus: string;
  status: string;
}

export interface EnterpriseSelectQueryResultDTO {
  roleId: number;
  name: string;
  outId: string;
  roleType: string;
}

export interface queryEnterpriseSelectListResult {
  data: EnterpriseSelectQueryResultDTO;
}

export interface queryEnterpriseListResult {
  dataList: EnterpriseItem[];
}

export interface IQueryEnterpriseDetailParams {
  roleId: number;
}

export interface SubEnterpriseItem {
  editVersion: any;
  outId: string;
  name: string;
  properties: any;
  roleId: any;
  enterpriseId: number; // 子企业 ID
  shortName: string; // 子企业简称
  fullName: string; // 子企业全称
  creditIdentifier: string; // 子企业信用标识符
  businessLicenseUrl: string;
  telephoneNumber: string;
  address: string;
  bankName: string;
  bankAccount: string;
  invoiceAddress: string;
  invoiceEmail: string;
  invoicePerson: string;
  invoiceTelephoneNumber: string;
}

export interface queryEnterpriseDetailResult {
  enterpriseId: number; // 企业 ID
  shortName: string; // 企业简称
  fullName: string; // 企业全称
  creditIdentifier: string; // 信用标识符
  businessLicenseUrl: string; // 营业执照 URL
  subList: SubEnterpriseItem[];
}

export interface IQueryProjectDetailParams {
  projectId: number;
}

export interface SingleProjectDetail {
  auditStatus?: string;
  projectId: number; // 项目 ID
  projectName: string; // 项目名称
  enterprise: {
    enterpriseId: number; // 企业 ID
    shortName: string; // 企业简称
  };
  contract: {
    contractId: number; // 合同 ID
    contractName: string; // 合同名称
    startDate: string; // 开始日期
    endDate: string; // 结束日期
    contractAmount: number; // 合同金额
  };
  invoice: {
    idenNumber: string; // 发票识别号
    title: string; // 发票抬头
    contactName: string; // 联系人姓名
    contactPhone: string; // 联系电话
    address: string; // 地址
    bankName: string; // 银行名称
    bankAccount: string; // 银行账号
  };
  settle: {
    rebateRate: string; // 返利率
    orderInterval: number; // 订单间隔
    settleIntervalType: string; // 结算间隔类型
    settleInterval: number; // 结算间隔
  };
  gmtCreate: string; // 创建时间
  gmtModified: string; // 修改时间
  updateOperator: string; // 更新操作人
  status: string; // 状态
}

export interface queryProjectDetailResult {
  data: SingleProjectDetail;
}

export interface IQueryMerchantListParams {
  currentPage: number;
  pageSize: number;
}

export interface MerchantItem {
  operationList: any;
  sellerId: number; // 商家ID
  taoSellerId: number; // 淘宝商家ID
  shortName: string; // 商家简称
  shopName: string; // 店铺名称
  contract: {
    contractId: number; // 合同ID
  };
  gmtCreate: string; // 创建时间
  gmtModified: string; // 修改时间
  updateOperator: string; // 更新操作人
  operateList: string[]; // 可进行的操作列表
  effectiveStatus: string; // 有效状态
  status: string; // 状态
}

export interface queryMerchantListResult {
  data: {
    dataList: MerchantItem[];
    totalCount: number;
  };
}

export interface IQueryMerchantDetailParams {
  roleId: number;
}

export interface MerchantItemDetail {
  operationList: never[];
  auditStatus: unknown;
  editVersion: unknown;
  outId: unknown;
  roleId: unknown;
  properties: any;
  sellerId: number; // 商家ID
  taoSellerId: number; // 淘宝商家ID
  name: string; // 商家简称
  shopName: string; // 店铺名称
  contract: {
    contractId: number; // 合同ID
    contractName: string; // 合同名称
    startDate: string; // 开始日期
    endDate: string; // 结束日期
  };
  invoice: {
    idenNumber: string; // 发票识别号
    title: string; // 发票抬头
    contactName: string; // 联系人姓名
    contactPhone: string; // 联系电话
    address: string; // 地址
    bankName: string; // 银行名称
    bankAccount: string; // 银行账号
  };
  settle: {
    rebateRate: string; // 返利率
    billCycle: number; // 账单周期
    billCycleType: string; // 账单周期类型
    settleCycle: number; // 结算周期
    settleCycleType: string; // 结算周期类型
    rateSellerLevel?: string; // 佣金优惠比率
  };
  gmtCreate?: string; // 创建时间
  gmtModified?: string; // 修改时间
  updateOperator?: string; // 更新操作人
  operateList?: string[]; // 可进行的操作列表
  effectiveStatus?: string; // 有效状态
  status: string; // 状态
}

export interface queryMerchantDetailResult {
  data: MerchantItemDetail;
}

export interface IEditSkuParams {
  poolId: number;
  itemId: number;
  skuList: Array<{
    skuId: string;
    ratioPrice: string;
    enable: boolean;
  }>;
}

export interface IQueryServicerListParams {
  currentPage: number;
  pageSize: number;
}

export interface ServicerItem {
  servicerId: number; // 服务商ID
  shortName: string; // 服务商简称
  contract: {
    contractId: number; // 合同ID
  };
  settle: {
    saleServiceRate: string; // 销售服务费率
  };
  gmtCreate: string; // 创建时间
  gmtModified: string; // 修改时间
  updateOperator: string; // 更新操作人
  operateList: string[]; // 可进行的操作列表
  effectiveStatus: string; // 有效状态
  status: string; // 状态
}

export interface queryServicerListResult {
  dataList: ServicerItem[];
}

export interface IQueryServicerDetailParams {
  servicerId: number;
}

export interface queryServicerDetailResult {
  data: ServicerItem;
}

export interface queryItemsPoolSkuListResult {
  data: {
    prop: string;
    skuId: number;
    skuPrice: string;
  }[];
}

export interface queryEnterpriseSelectParams {
  roleId?: number;
}

export interface queryEnterpriseSelectResult {
  data?: any[];
}

export interface queryEnterpriseOrderListQuery {
  contractId?: string;
  bizOrderIdList?: string | string[];
  outOrderIdList?: string | string[];
  payStatus?: number;
  status?: number;
  logisticStatus?: number;
  createTime?: any;
  createTimeStart?: string;
  createTimeEnd?: string;
  buyerId?: number;
}
export interface queryEnterpriseOrderListParams {
  contractOrderListReq: queryEnterpriseOrderListQuery
  pageQueryParam: {
    pageSize: number;
    currentPage: number;
  }
}

export interface queryEnterpriseOrderListResult {
  pageData?: {
    data:  any[]
    currentPage: number;
    pageSize: number;
    totalCount: number;
  };
  errorMsg: string;
}

export interface queryEnterpriseOrderDetailParams {
  bizOrderId: string
  buyerId: string
}

export interface queryEnterpriseOrderDetailRes {
  bizOrderId?: string;
  outOrderId?: string;
  createTime?: string;
  socialCreditCode: string;
  payInfo: {
    totalPrice: number;
  };
  buyerId: string;
  contractNo: string;
  outServiceId: string;
  outServiceNick: string;
  receiver: {
  receiverName: string;
    mobile: string;
    addressDetail: string;
  };
  subOrderDetails: Array<{
    itemId: string;
    skuId: string;
    itemPrice: number;
    buyAmount: number;
    logistics: Array<{ standardDesc: string }>;
    tpOrderId: string;
  }>;
}
export interface queryEnterpriseOrderDetailResult {
  data?: {
    data?: queryEnterpriseOrderDetailRes;
  };
  errorMsg: string;
}

export interface ContractBaseInfo {
  otherCompanyName: string;
  endDate: number;
  contractId: string;
  contractAmount: string;
  contractName: string;
  detailUrl: string;
  creditAmount: string | null;
  class: string;
  startDate: number;
}

export interface queryContractBaseInfoResult {
  data: ContractBaseInfo;
}

export interface queryEnterpriseSelectListExtendResult {
  data: {
    roleId: number;
    name: string;
    auditStatus: string;
    outId: string;
    roleType: string;
    editVersion: number;
    class: string;
    flowId: string;
    version: number;
    properties: {
      bankAccount: string;
      invoiceTelephoneNumber: string;
      address: string;
      telephoneNumber: string;
      businessLicenseUrl: string;
      branchName: string;
      bankName: string;
      invoiceAddress: string;
      invoiceEmail: string;
      invoicePerson: string;
      branchNo: string;
    };
    status: string;
  }[];
}
export interface queryTaobaoMerchantInfoResult {
  data: {
    sellerId: number;
    companyName: string;
    creditIdentifier: string;
    sellerName: string;
  }
}

export interface queryCorporateBillItemParams {
  batchSettleOrderNo: string;
}
export interface queryCorporateBillItemResult {

}

export interface queryCorporateBillListParams {
  billDate: string;
  batchSettleOrderNo: string;
  buyerId: string;
  pageParam: {
    pageSize: number,
    currentPage: number,
  }
}

export interface queryCorporateBillListItem {

}
export interface queryCorporateBillListResult {
  pageData: {
    data: queryCorporateBillListItem[],
    currentPage: number,
    pageSize: number,
    totalCount: number,
    totalPage: number
  }
}
export interface queryCorporateBillDetailParams {
  id?: string;
  ids?: string[];
  batchSettleOrderNo?: string;
  pageParam?: {
    pageSize: number,
    currentPage: number,
  }
}

export interface queryCorporateBillDetailItem {
  extInfo: {
    taxRate: string;
  }
  amount: number;
}
export interface queryCorporateBillDetailResult {
  pageData: {
    data: queryCorporateBillDetailItem[],
    currentPage: number,
    pageSize: number,
    totalCount: number,
    totalPage: number
  }
}

export interface queryUpdateBillStatusParams {

}

export interface queryUpdateBillStatusResult {
  errorMsg: string;
  success: boolean;
}

export interface queryCreateBatchOrder {
  buyerId: string;
  batchSettleName: string;
  contractId: string;
  settleEndDate: string;
  operator: string;
}

export interface queryCreateBatchOrderResult {
  errorMsg: string;
}

export interface queryApplySalesInvoice {
  batchSettleOrderNo: string;
  saleInvoiceMemo?: string;
}

export interface queryApplySalesInvoiceResult {
  errorMsg?: string;
}

export interface queryGetSalesInvoice {
  batchSettleOrderNo: string;
  buyerId: number | undefined;
}
export interface queryGetSalesInvoiceResult {
  errorMsg?: string;
}
