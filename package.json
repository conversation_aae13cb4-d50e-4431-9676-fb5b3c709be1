{"name": "@ali/bzb-scaffold", "version": "1.4.0", "description": "淘系运营工作台子应用脚手架", "dependencies": {"@ali/bzb-request": "^2.5.2", "@ali/universal-mtop": "^7.0.0", "@alifd/next": "^1.x", "@alife/theme-xiaoer-v23": "0.7.2", "@ice/stark-app": "^1.2.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "clipboard": "^2.0.11", "clsx": "^2.1.1", "esbuild": "^0.25.4", "jszip": "^3.10.1", "moment": "^2.24.0", "react": "^18.0.0", "react-dom": "^18.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@ali/eslint-config-att": "^1.0.6", "@ali/ice-plugin-def": "^1.2.4", "@ali/ice-plugin-spm": "^3.0.1", "@ice/plugin-pha": "^3.0.4", "@iceworks/spec": "^1.0.0", "build-plugin-fusion": "^0.1.0", "build-plugin-icestark": "^2.0.0", "build-plugin-moment-locales": "^0.1.0", "eslint": "^8.0.0", "ice.js": "^1.0.0", "stylelint": "^13.7.2", "typescript": "^4.9.5"}, "scripts": {"start": "icejs start", "build": "icejs build", "prepublishOnly": "npm run build", "lint": "npm run eslint && npm run stylelint", "eslint": "eslint --cache --ext .js,.jsx,.ts,.tsx ./", "eslint:fix": "npm run eslint -- --fix", "stylelint": "stylelint \"**/*.{css,scss,less}\"", "update-lockfile": "rm -rf package-lock.json && rm -rf node_modules && tnpm i"}, "publishConfig": {"registry": "http://registry.npm.alibaba-inc.com"}, "scaffoldConfig": {"name": "bzb-scaffold", "title": "淘系运营工作台子应用脚手架", "categories": ["基础模板"], "screenshot": "https://img.alicdn.com/tfs/TB1CK4HchD1gK0jSZFKXXcJrVXa-2880-1372.png"}, "repository": "**************************:bzb-westeros/biz-purchase.git", "engines": {"node": ">=16.0.0"}}